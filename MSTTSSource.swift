//
//  MSTTSLibrary.swift
//  ParsingBook
//
//  Created by <PERSON><PERSON><PERSON> on 2024/8/7.
//

import UIKit
import Starscream
import AVFoundation
import SwiftyJSON
import PromiseKit


// MARK: - 微软大声朗读
class MSTTSSource: NSObject {
    /// 最大重试次数
    private let maxRetryCount = 3
    /// 当前重试次数
    private var retryCount = 0
    
    weak var delegate: ReadVoiceLibraryDelegate?
    /// 连接成功回调
    private var connectCallback: ((_ response: WebSocketResponse) -> Void)?
    /// 下载回调
    private var downloadCallback: ((_ response: WebSocketResponse) -> Void)?

    /// 是否连接socket
    private var isConnected = false
    /// socket连接
    private var socket: WebSocket!
    private let server = WebSocketServer()
    
    private var roleData = VoiceRoleModel()
    
    private var text = ""
    
    private let uuid = UUID().uuidString
    /// 音频文件
    private var audioData = Data()
    
    /// 请求合成单个音频文件
    /// - Parameters:
    ///   - text: 要合成的文本
    ///   - roleData: 语音角色模型
    ///   - fileName: 保存的文件名
    /// - Returns: Promise对象
    func request(text: String, roleData: VoiceRoleModel, fileName: String) -> Promise<URL> {
        guard !roleData.voiceType.isEmpty else {
            return Promise(error: NSError(domain: "MSTTSSourceError", code: -1, userInfo: [NSLocalizedDescriptionKey: "朗读音色playIdent为空"]))
        }
        self.roleData = roleData
        self.text = text
        return firstly {
            return self.checkIfFileExists(fileName: fileName)

        }.then { fileURL -> Promise<URL> in
            if let fileURL = fileURL {
                return Promise.value(fileURL) // 返回已存在的 URL
            }
            return self.retryDownload(fileName: fileName, text: text)
            
        }.ensure {
            self.socket?.disconnect()
        }
    }
    
    
    /// 重试下载
    private func retryDownload(fileName: String, text: String) -> Promise<URL> {
        return firstly {
            self.connectWebSocket()
        }.then {
            return self.sendDownloadRequest(text: text)
        }.then { (text, data) in
            let suffix = data.getAudioSuffix()
            let config = KAVoiceFilesConfig()
            config.fileName = fileName
            config.suffix = suffix
            config.text = text
            return self.saveFile(data: data, config: config)
        }.recover { error -> Promise<URL> in
            print("下载或处理失败，尝试重新连接: \(error.localizedDescription)")
            if self.retryCount < self.maxRetryCount {
                self.retryCount += 1
                return self.retryDownload(fileName: fileName, text: text) // 重试
            } else {
                throw error // 超过重试次数，抛出错误
            }
        }
    }
    
    /// 初始化socket，每次下载都重置一次
    private func connectWebSocket() -> Promise<Void> {
        return Promise { seal in
            let wss = "wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1?\(self.getParamsString())"
            var request = URLRequest(url: URL(string: wss)!)
            request.timeoutInterval = 5
            request.setValue("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/114.0.1823.67", forHTTPHeaderField: "User-Agent")
            request.setValue("chrome-extension://jdiccldimpdaibmpdkjnbmckianbfold", forHTTPHeaderField: "Origin")
            
            self.socket = WebSocket(request: request)
            self.socket.delegate = self
            self.socket.connect()
            // 连接成功回调
            self.connectCallback = { response in
                switch response {
                case .success:
                    seal.fulfill(())
                case .failure(let error):
                    seal.reject(error)
                }
            }
        }
    }
    
    /// 发送合成请求
    private func sendDownloadRequest(text: String) -> Promise<(text: String, data: Data)> {
        return Promise { seal in
            let time = Date()
            let uuid = self.uuid
            let format = "audio-24khz-96kbitrate-mono-mp3"
            let msg1 = "Path: speech.config\r\nX-RequestId: \(uuid)\r\nX-Timestamp: \(time)\r\nContent-Type: application/json\r\n\r\n{\"context\":{\"system\":{\"name\":\"SpeechSDK\",\"version\":\"1.19.0\",\"build\":\"JavaScript\",\"lang\":\"JavaScript\",\"os\":{\"platform\":\"Browser/Linux x86_64\",\"name\":\"Mozilla/5.0 (X11; Linux x86_64; rv:78.0) Gecko/20100101 Firefox/78.0\",\"version\":\"5.0 (X11)\"}}}}"
            self.socket.write(string: msg1)
            let msg2 = "Path: synthesis.context\r\nX-RequestId: \(uuid)\r\nX-Timestamp: \(time)\r\nContent-Type: application/json\r\n\r\n{\"synthesis\":{\"audio\":{\"metadataOptions\":{\"bookmarkEnabled\":false,\"sentenceBoundaryEnabled\":false,\"visemeEnabled\":false,\"wordBoundaryEnabled\":false},\"outputFormat\":\"\(format)\"}}}"
            self.socket.write(string: msg2)
            
            let newText = text.removeSEHeadAndTail

            let ssml = """
            <speak xmlns="http://www.w3.org/2001/10/synthesis" xmlns:mstts="http://www.w3.org/2001/mstts" xmlns:emo="http://www.w3.org/2009/10/emotionml" version="1.0" xml:lang="en-US"><voice name="\(self.roleData.voiceType)"><prosody rate="0%" pitch="0%">\(newText)</prosody></voice></speak>
            """
            
            let msg = "Path: ssml\r\nX-RequestId: \(uuid)\r\nX-Timestamp: \(time)\r\nContent-Type: application/ssml+xml\r\n\r\n\(ssml)"
            self.socket.write(string: msg)
            
            self.setDownloadCallback { response in
                switch response {
                case .success:
                    seal.fulfill((text, self.audioData))
                case .failure(let error):
                    print("mstt处理音频文件错误:\(error)-\(self.text)")
                    seal.reject(error)
                }
            }
        }
    }
    
    /// 检查文件是否已存在
    private func checkIfFileExists(fileName: String) -> Promise<URL?> {
        return Promise { seal in
            let fileURL: URL? = KAVoiceFilesConfig.getFile(path: .history(.readPlay), fileName: fileName)?.url
            seal.fulfill(fileURL)
        }
    }
    
    /// 处理音频文件
    private func handleAudioData() -> Promise<Data> {
        return Promise { seal in
            
        }
    }
    
    /// 设置下载回调
    private func setDownloadCallback(_ callback: @escaping (_ response: WebSocketResponse) -> Void) {
        guard self.downloadCallback == nil else {
            print("⚠️ downloadCallback 已存在，避免覆盖")
            return
        }
        self.downloadCallback = callback
    }
    
    /// 保存文件
    private func saveFile(data: Data, config: KAVoiceFilesConfig) -> Promise<URL> {
        return Promise { seal in
            guard !data.isEmpty else {
                seal.reject(NSError(domain: "大声朗读", code: 1001, userInfo: [NSLocalizedDescriptionKey: "文件下载失败"]))
                return
            }
            do {
                let file = try KAVoiceFilesConfig.saveFile(path: .history(.readPlay), config: config, data: data)
                print("mstt保存文件成功:\(file.url)")
                seal.fulfill(file.url)
            } catch {
                print("mstt保存文件错误:\(error)")
                seal.reject(error)
            }
        }
    }

    /// 清理数据
    private func clearData() {
        // 保持文件失败，清理并终止
        self.audioData.removeAll()
    }
    
    /// 退出并断开连接
    func outAction() {
        print("退出并断开连接")
        self.socket?.disconnect()
        self.socket = nil
        
        self.clearData()
        self.connectCallback = nil
        self.downloadCallback = nil
    }
    
    deinit {
        print("Socket销毁")
    }
}

// MARK: - WebSocket监听方法
extension MSTTSSource: WebSocketDelegate {
    
    func didReceive(event: WebSocketEvent, client: WebSocketClient) {
        switch event {
        case .connected(let headers):// 连接成功
            self.isConnected = true
//            print(">>>>>>websocket已连接: \(headers)")
            self.connectCallback?(.success)
            
        case .disconnected(let reason, let code): // 断开连接
            self.isConnected = false
//            print(">>>>>>Websocket断开连接: \(reason) with code: \(code)")
            let code = WebSocketStatusCode(rawValue: code) ?? .OtherError
            self.delegate?.didDisconnect(reason: reason, code: code)
            
        case .text(let string):
//            print("Received text: \(string)")
            break
        case .binary(let data):
            if let range = data.range(of: "Path:audio\r\n".data(using: .utf8)!) {
                let startIndex = range.upperBound
                let d = data[startIndex...]
//                print(">>>>接收文件-->\(self.text):\(d)")
                self.audioData.append(d)
                if d.isEmpty {
                    self.downloadCallback?(.success)
                }
            } else {
                print("未找到目标字符串")
            }
        case .ping:
            break
        case .pong:
            break
        case .viabilityChanged(_):
            break
        case .reconnectSuggested(_):
            break
        case .cancelled: // 取消连接
            self.isConnected = false
        case .error(let error as HTTPUpgradeError):
//            self.isConnected = false
            self.downloadCallback?(.failure(error))
            
            var newError: Error
            switch error {
            case .invalidData:
                newError = NSError(domain: "invalidData", code: 400, userInfo: [NSLocalizedDescriptionKey: "获取到无效的数据"])
            
            case .notAnUpgrade(let code, let value):
                newError = NSError(domain: "notAnUpgrade", code: code, userInfo: [NSLocalizedDescriptionKey: "notAnUpgrade\(code)"])
            }
//            self.delegate?.didFailWithError(newError)
            
        case .peerClosed:
            break
        case .error(let error):
//            if let posixError = error as? POSIXError, posixError.code == .ECONNRESET {
            print("连接被对方重置，尝试重新连接...\(error)\nself.downloadCallback:\(self.downloadCallback)")
//            }
            if let error = error {
                self.downloadCallback?(.failure(error))
            }
//            self.socket.connect()
        }
    }
    
    /// msgkey
    private func getMSGECKey() -> String {
        // 获取当前UTC时间（以100纳秒为单位，从1601年1月1日算起）
        var t = Date().timeIntervalSince1970 * 10_000_000 + 116_444_736_000_000_000
        
        // 调整 t，使其为不超过 3,000,000,000 的最大倍数
        t -= t.truncatingRemainder(dividingBy: 3_000_000_000)
        // 将t转换为字符串，并追加固定字符串
        let str = "\(Int64(t))6A5AA1D4EAFF4E9FB37E23D68491D6F4"
        // 计算 SHA-256 哈希
        let hash = str.bytes.sha256()
        // 转换哈希值为大写的十六进制字符串
        let hashHex = hash.map { String(format: "%02X", $0) }.joined()
        return hashHex
    }

    /// 参数拼接
    private func getParamsString() -> String {
        let token = "6A5AA1D4EAFF4E9FB37E23D68491D6F4"
        let msGecKey = getMSGECKey()
        let msGecVersion = "1-132.0.2917.0"
        let jsonDict: [String: Any] = [
            "TrustedClientToken": token,
            "ConnectionId": self.uuid,
            "Sec-MS-GEC": msGecKey,
            "Sec-MS-GEC-Version": msGecVersion
        ]
        let queryString = jsonDict.map { "\($0.key)=\($0.value)" }
            .joined(separator: "&")
        return queryString
    }
}

enum WebSocketResponse {
    case success
    case failure(Error)
}

enum WebSocketStatusCode: UInt16 {
    /// 正常关闭
    case CLOSE_NORMAL = 1000
    /// 终端离开, 可能因为服务端错误, 也可能因为浏览器正从打开连接的页面跳转离开。
    case CLOSE_GOING_AWAY = 1001
    /// 由于协议错误而中断连接。
    case CLOSE_PROTOCOL_ERROR = 1002
    /// 由于接收到不允许的数据类型而断开连接 (如仅接收文本数据的终端接收到了二进制数据)。
    case CLOSE_UNSUPPORTED = 1003
    /// 保留。 表示没有收到预期的状态码。
    case CLOSE_NO_STATUS = 1005
    /// 由于收到了格式不符的数据而断开连接
    case UnsupportedData = 1007
    /// 由于收到不符合约定的数据而断开连接。 这是一个通用状态码, 用于不适合使用 1003 和 1009 状态码的场景。
    case PolicyViolation = 1008
    /// 由于收到过大的数据帧而断开连接。
    case CLOSE_TOO_LARGE = 1009
    /// 客户端期望服务器商定一个或多个拓展, 但服务器没有处理, 因此客户端断开连接。
    case MissingExtension = 1010
    /// 客户端由于遇到没有预料的情况阻止其完成请求, 因此服务端断开连接。
    case InternalError = 1011
    /// 服务器由于重启而断开连接。
    case ServiceRestart = 1012
    /// 其他错误
    case OtherError = 9999
}
