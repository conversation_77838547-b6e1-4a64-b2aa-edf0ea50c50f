//
//  ReadPlayProtocol.swift
//  ParsingBook
//
//  Created by <PERSON><PERSON><PERSON> on 2024/8/9.
//

import UIKit
import AVFAudio

/// 默认播放速度
let DefaultVoiceSpeed: Float = 1
/// 默认音调
let DefaultVoicePitch: Float = 1
/// 播放器播放速率最小值
let AVPlayerRateMin: Float = 0.5
/// 播放器播放速率最大值
let AVPlayerRateMax: Float = 3

protocol ReadVoiceProtocol: NSObject {
    /// 播放角色
    var playRole: String { get set }
    /// 播放速度
    var playSpeed: Float { get set }
    /// 播放音调
    var playPitch: Float { get set }
    
    /// 播放
    func play(asset: PlayURLAsset) throws
    /// 调整语速
    func setRate(value: Float)
    /// 停止播放
    func stop()
    /// 暂停播放
    func pause()
    /// 恢复播放
    func resume()
}

enum PlayURLAsset {
    /// 播放地址
    case url(url: URL?, header: [String: String]?, fileName: String)
    /// 播放本地
    case path(url: URL?, ident: String, fileName: String)
    /// 文件路径
    case file(path: URL)
    /// 播放文字
    case text(String)
    /// 自定义文件名称
    var fileName: String {
        get {
            switch self {
            case .url(_, _, let fileName):
                return fileName
            case .text(let text):
                return text
            case .file(let path):
                return path.absoluteString
            case .path(_, _, let fileName):
                return fileName
            }
        }
    }
}

/// 阅读播放库公共代理
protocol ReadVoiceLibraryDelegate: NSObjectProtocol {
    
    /// 播放进度（听书）
    /// - Parameters:
    ///   - totalProgress: 总进度
    ///   - currentProgress: 当前进度
    func playProgress(totalProgress: Float64, currentProgress: Float64)
    
    /// 播放开始加载
    func playStartLoad()
    /// 断开连接
    func didDisconnect(reason: String, code: WebSocketStatusCode)
    
    /// 播放失败
    func didFailWithError(_ error: Error?)
    
    /// 开始播放
    func didStartPlaying()
    /// 播放完成
    func didFinishPlaying()
    
    /// 暂停播放音频
    func didPausePlaying()
    
    /// 继续播放音频
    func didResumePlaying()
    
    /// 取消播放
    func didCancelPlaying()
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, willSpeakRangeOfSpeechString characterRange: NSRange, utterance: AVSpeechUtterance)
}

protocol ReadPlayManageDelegate: NSObjectProtocol {
 
    /// 当前播放
    /// - Parameters:
    ///   - manage: 管理类
    ///   - pageData: 当前章节
    ///   - paragraphData: 当前段落
    func readPlay(_ manage: ReadVoiceManage, currentPlaying pageData: KAPageStyle?, paragraphData: ParagraphSegmentModel?)
    
    /// 下一页没有播放内容了
    func readPlayNotNextPageData(_ manage: ReadVoiceManage, pageData: KAPageStyle?)
    
    /// 上一页没有播放内容了
    func readPlayNotPreviousPageData(_ manage: ReadVoiceManage, pageData: KAPageStyle?)
    
    /// 开始播放
    func readPlay(_ manage: ReadVoiceManage, startPlaying pageData: KAPageStyle?)
    
    /// 停止播放
    func readPlay(_ manage: ReadVoiceManage, stopPlaying pageData: KAPageStyle?)
    
    /// 暂停播放
    func readPlay(_ manage: ReadVoiceManage, didPausePlaying pageData: KAPageStyle?, isHeadphones: Bool)
    
    /// 翻到上一页
    /// - Parameters:
    ///   - previousPageData: 上一页分页数据
    ///   - oldPageData: 翻之前的分页数据
    func readPlay(_ manage: ReadVoiceManage, previousPageData: KAPageStyle, oldPageData: KAPageStyle)
    
    
    /// 翻到上一章（听书时使用）
    /// - Parameters:
    ///   - manage:
    ///   - previousCatalogData: 上一章
    ///   - oldCatalogData: 当前章
    func readPlay(_ manage: ReadVoiceManage, previousCatalogData: ReadCatalogModel, oldCatalogData: ReadCatalogModel)
    
    /// 翻到下一页
    /// - Parameters:
    ///   - nextPageData: 下一页分页数据
    ///   - oldPageData: 翻之前的分页数据
    func readPlay(_ manage: ReadVoiceManage, nextPageData: KAPageStyle, oldPageData: KAPageStyle)
    
    /// 翻到下一章（听书时使用）
    /// - Parameters:
    ///   - manage:
    ///   - previousCatalogData: 上一章
    ///   - oldCatalogData: 当前章
    func readPlay(_ manage: ReadVoiceManage, nextCatalogData: ReadCatalogModel, oldCatalogData: ReadCatalogModel)
    
    /// 继续播放
    func readPlay(_ manage: ReadVoiceManage, didContinuePlaying pageData: KAPageStyle?)
 
}


// 多播代理类
class ReadPlayMulticastDelegate<T> {
    
    private var delegates = NSHashTable<AnyObject>.weakObjects()
    
    func addDelegate(_ delegate: T) {
        self.delegates.add(delegate as AnyObject)
    }
    
    func removeDelegate(_ delegate: T) {
        self.delegates.remove(delegate as AnyObject)
    }
    
    /// 调用
    func invoke(_ invocation: (T) -> Void) {
        for delegate in self.delegates.allObjects {
            if let delegate = delegate as? T {
                invocation(delegate)
            }
        }
    }
}

enum ReadPlayError: Error {
    /// 无重要参数
    case notParam(String)
    /// 网络失败
    case network(String)
    
    case fileSizeEmpty
    /// 无数据
    case notData(String)
    /// 重复字段
    case repetition(String)
    /// 数据库错误
    case databaseError(String)
    /// 其他错误
    case otherError(String)
    
    var localizedDescription: String {
        switch self {
        case .notParam(let message):
            return "缺少参数: \(message)"
        case .notData(let message):
            return "数据为空: \(message)"
        case .repetition(let message):
            return "重复字段: \(message)"
        case .databaseError(let message):
            return "数据库错误: \(message)"
        case .otherError(let message):
            return message
        case .fileSizeEmpty:
            return "文件大小0KB"
        case .network(let message):
            return "网络错误: \(message)"
        }
    }
}


