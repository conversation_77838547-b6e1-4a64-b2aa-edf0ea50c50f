//
//  ReadPlayManage.swift
//  ParsingBook
//
//

import UIKit
import AVFoundation
import MediaPlayer
import Files

// MARK: - 阅读播放管理类
class ReadVoiceManage: NSObject {
    
    private var isTest = false
    /// 下载管理类
    private var download: ReadVoiceDownload?
    
    // 🔧 添加Promise持有机制，防止Promise被提前释放
    private var activePromises: [Any] = []
    private let promiseQueue = DispatchQueue(label: "com.readvoice.manage.promise", attributes: .concurrent)
    
    static var shared = ReadVoiceManage()
    
    private let multicastDelegate = ReadPlayMulticastDelegate<ReadPlayManageDelegate>()
        
    func addDelegate(_ delegate: ReadPlayManageDelegate) {
        self.multicastDelegate.addDelegate(delegate)
    }
    
    func removeDelegate(_ delegate: ReadPlayManageDelegate) {
        self.multicastDelegate.removeDelegate(delegate)
    }
    /// 段落句子
    private var paragraphDatas = [ParagraphSegmentModel]()
        
    /// 当前阅读书籍(进入阅读页面)
    private var readBookData: ReadBookModel?
    
    /// 书籍类型，用于朗读书籍
    var bookType: BookReadTypeMode {
        get { return self.getPlayBookData().typeEnum }
    }
    /// 播放类型（朗读和听书的类型）
    var playData: PlayModel!
    
    /// 声明一个属性来保存定时器
    private var timer: Timer?
    /// 设置的分钟数
    var remainingSeconds: Int = 0
    /// 总的秒数，根据 remainingSeconds * 60
    private var totalSecondTime: Int = 0
    
    var readColor = UIColor.black.withAlphaComponent(0.2)
    
    /// 数据源
    private var readSource: ReadSource?
    
    /// 播放配置
    private var voiceConfig: KAVoiceConfig {
        get { KAReadConfig.shared.getData().voiceConfig }
    }
    /// 朗读播放规则
    private var voiceRuleData: VoiceRuleModel?
    
    /// 当前播放音色
    private var roleData: VoiceRoleModel {
        get { self.voiceConfig.playRole }
    }
  
    /// 当前状态类型 - 当前的书籍对比播放的书籍
    /// - Parameter bookData: 当前书籍
    /// - Returns: 返回状态类型
    func getStatusType(bookData: ReadBookModel?) -> StatusType {
        guard self.isWorking else {
            return .none
        }
        // 如果阅读书籍为空，仅播放模式
        guard let bookData = bookData else {
            return .onlyPlay
        }
        // 对比阅读书籍和朗读书籍是否一致，保持一致需要处理阅读界面业务
        if bookData.bookId == self.readSource?.getBookData().bookId {
            return .readAndPlay
        }
        return .readOrPlayUnlike
    }
    
    /// 对比播放章节与传入章节不同
    /// - Parameters:
    ///   - bookData: 当前书籍，id比对
    ///   - catalogData: 当前章节，index比对
    /// - Returns: 结果是否相同，如果相同直接打开播放界面
    func isPlayCatalogUnlike(bookData: ReadBookModel, catalogData: ReadCatalogModel) -> Bool {
        let playBookData = self.readSource?.getBookData()
        let isBook = bookData.bookId == playBookData?.bookId
        let isCatalog = catalogData.catalogIndex == self.readSource?.catalogIndex
        return isBook && isCatalog
    }
    
    /// 阅读专用类型判断
    var readStatusType: StatusType {
        get {
            self.getStatusType(bookData: self.readBookData)
        }
    }

    /// 下一段类型
    var nextPlayType = NextPlayType.currentPage
    
    /// 当前播放内容
    private var playContent: CatalogStyle?
    
    /// 当前阅读段落数据
    var currentParagraphData: ParagraphSegmentModel? {
        get {
            guard self.currentParagraphIndex < self.paragraphDatas.count else {
                return nil
            }
            return self.paragraphDatas[self.currentParagraphIndex]
        }
    }
    
    /// 当前页面
    var currentPageData: KAPageStyle? {
        get { return self.readSource?.pageData }
    }
    /// 当前阅读段落索引（整个章节段落的索引）
    private var currentParagraphIndex = 0
    
    /// 已预加载的段落索引集合
    private var preloadedParagraphIndexes: Set<Int> = []
    
    /// 播放完毕时间记录（用于性能监测）
    private var playFinishTime: Date?
    /// 下一段开始播放时间记录（用于性能监测）
    private var nextPlayStartTime: Date?
    
    /// 锁屏信息
    private var playingInfoData: ReadPlayingInfo? {
        didSet {
            guard let data = self.playingInfoData else {
                return
            }
            // 如果等于空，默认一个图
            if data.coverImage == nil {
                self.playingInfoData?.coverImage = BookCoverImage(frame: CGRect(x: 0, y: 0, width: 90, height: 70), bookName: data.title, author: data.author).image()
            }
            // 设置锁屏信息
            self.setPlayingInfoCenter()
        }
    }
    
    private let center = MPRemoteCommandCenter.shared()
    
    /// 听书正在执行(暂停也是执行中)
    var isWorking = false
    /// 暂停播放
    var isPaused: Bool {
        get {
            switch self.playData {
            case .read:
                switch self.roleData.playMode {
                case .speech:
                    return self.speechLibrary?.isPaused ?? false
                case .audio:
                    return self.qPlayerLibrary?.isPaused ?? false
                }
            case .play:
                return self.playLibrary?.isPaused ?? false
            case .file:
                return self.playLibrary?.isPaused ?? false
            case .none:
                return false
            }
        }
        set {
            switch self.playData {
            case .read:
                switch self.roleData.playMode {
                case .speech:
                    self.speechLibrary?.isPaused = newValue
                case .audio:
                    self.qPlayerLibrary?.isPaused = newValue
                }
            case .play:
                self.playLibrary?.isPaused = newValue
            case .file:
                self.playLibrary?.isPaused = newValue
            case .none:
                break
            }
            self.playVC?.isPaused(newValue)
        }
    }
    /// 系统语音合成（用于朗读）
    private var speechLibrary: ReadSpeechLibrary?
    
    private var qPlayerLibrary: ReadQueuePlayerLibrary?

    /// 播放器模式（用于听书）
    private var playLibrary: ReadAVPlayLibrary?
    
    /// 播放开关
    lazy var playButton: ReadPlayHoverView = {
        let playButton = ReadPlayHoverView()
        playButton.coverImage.addTarget { [weak self] in
            guard let self = self else { return }
            self.openReadPlayVC()
        }
        playButton.closeButton.addTarget(self, action: #selector(self.closePlayButtonAction), for: .touchUpInside)
        playButton.playButton.addTarget(self, action: #selector(self.playButtonAction), for: .touchUpInside)
        return playButton
    }()
    
    /// 播放控制器
    private var playVC: ReadPlayController?
    
    /// 设置书籍数据
    private func setBookData(bookData: ReadBookModel) {
        self.readSource = ReadSource(book: bookData)
        // 初始化阅读实体类
        let promise = self.readSource?.initializeCatalogData(bookData: bookData, modeStyle: .play).done {_ in 
            self.updateReadPlayVC()
            self.playContent = self.readSource?.currentCatalogData?.styleData
        }.catch { error in
            BoneHUD.shared.show(error: error)
        }
        
        // 🔧 持有Promise引用
        if let promise = promise {
            self.promiseQueue.async(flags: .barrier) {
                self.activePromises.append(promise)
            }
        }
    }
    
    /// 获取当前播放书籍
    func getPlayBookData() -> ReadBookModel {
        return self.readSource?.getBookData() ?? ReadBookModel()
    }
    
    /// 更新播放界面UI
    func updateReadPlayVC() {
        self.playButton.playButton.isSelected = self.isPaused
        if self.playVC != nil {
            self.playVC?.setPlayText(self.currentParagraphData?.text ?? "")
            self.playVC?.isPaused(self.isPaused)
        }
    }
    
    func updateReadPlayData() {
        self.playButton.playData = self.playData
        if self.playVC != nil {
            self.playVC?.playData = self.playData
        }
    }
    
    override init() {
        super.init()
        print("加载播放管理器一次")
        // 定期清理
        self.regularCleanData()

        // 用于监听音频会话的中断事件 电话呼入、闹钟响起、其他应用程序开始播放音频等
        NotificationCenter.default.addObserver(self, selector: #selector(handlePhoneCall(notification:)), name: AVAudioSession.interruptionNotification, object: nil)
        // 监听蓝牙,耳机插拔、蓝牙设备连接、音频输出源切换等
        NotificationCenter.default.addObserver(self, selector: #selector(audioRouteChanged(notification:)), name: AVAudioSession.routeChangeNotification, object: nil)
        
        // 播放
        self.center.playCommand.addTarget { event in
//            print("1.播放event.command:\(event.command)")
            self.continueSpeaking()
            return .success
        }
        
        // 暂停
        self.center.pauseCommand.addTarget { event in
            print("1.暂停event.command:\(event.command)")
            self.pauseSpeaking()
            return .success
        }
        // 下一首
        self.center.nextTrackCommand.addTarget { event in
            switch self.playData {
            case .read:
                self.nextReadPage()
            case .play:
                self.nextCatalog()
            case .file:
                self.nextFile()
            case .none:
                break
            }
            return .success
        }
        // 上一首
        self.center.previousTrackCommand.addTarget { event in
            switch self.playData {
            case .read:
                self.previousReadPage()
            case .play:
                self.previousCatalog()
            case .file:
                self.previousFile()
            case .none:
                break
            }
            return .success
        }
        // 处理播放进度拖动
        self.center.changePlaybackPositionCommand.addTarget { [weak self] event in
            guard let self = self, let event = event as? MPChangePlaybackPositionCommandEvent else {
                return .commandFailed
            }
            // 获取当前播放的总时长
            guard let duration = self.playLibrary?.avPlayer?.currentItem?.duration.seconds, duration > 0 else {
                return .commandFailed
            }
            
            // 计算 0-1 的进度比例
            let progress = event.positionTime / duration
            
            self.playLibrary?.setSeek(value: progress)
            self.setProgressUI()
            return .success
        }
    }
    
    /// 设置段落索引
    /// - Parameters:
    ///   - dbChapterData: 章节信息
    ///   - pageData: 当前分页
    private func setParagraphIndex(catalogStyle: CatalogStyle, pageData: KAPageStyle) {
        // 根据当前分页定位开始朗读的段落
        self.currentParagraphIndex = self.paragraphDatas.firstIndex { paragraphData in
            // 定位当前页第一个段落的位置
            return pageData.range.location <= paragraphData.range.location
        } ?? 0
    }
    
    /// 进入阅读
    func pushReading(book: ReadBookModel) {
        self.readBookData = book
    }
    /// 退出阅读，清空当前阅读书籍
    func outReading() {
        // 清空
        self.readBookData = nil
    }
    
    /// 当前播放事件
    private func currentPlaying() {
        let currentPageContent = self.currentPageData
        self.multicastDelegate.invoke { delegate in
            delegate.readPlay(self, currentPlaying: currentPageContent, paragraphData: self.currentParagraphData)
        }
        // 刷新进度
        self.setProgressUI()
        self.updateReadPlayVC()
        BoneHUD.shared.dismiss()
    }
        
    /// 阅读上一页
    private func previousReadPage() {
        guard let previousPageData = self.readSource?.previousPageData,
              let oldPageData = self.currentPageData else {
            return
        }
        // 更新阅读记录
        do {
            // 阅读下一页前运行代理方法，方便阅读界面对比是否在当前页面，只有当前页面才自动翻页
            // 代理方法
            self.multicastDelegate.invoke { delegate in
                delegate.readPlay(self, previousPageData: previousPageData, oldPageData: oldPageData)
            }
            try self.setNewPlay(catalogIndex: previousPageData.catalogIndex, pageIndex: previousPageData.pageNum)
            // 初始化播放
            self.initializeSpeek()
        } catch {
            BoneHUD.shared.show(error: error)
            print("朗读上一页错误:\(error.localizedDescription)")
        }
    }
    
    /// 阅读下一页
    private func nextReadPage() {
        guard let nextPageData = self.readSource?.nextPageData, let oldPageData = self.currentPageData else {
            BoneHUD.shared.show(status: "已播完最新章节", showType: .info)
            self.stopSpeaking()
            return
        }
        // 更新阅读记录
        do {
            // 阅读下一页前运行代理方法
            // 代理方法
            self.multicastDelegate.invoke { delegate in
                delegate.readPlay(self, nextPageData: nextPageData, oldPageData: oldPageData)
            }
            try self.setNewPlay(catalogIndex: nextPageData.catalogIndex, pageIndex: nextPageData.pageNum)
            self.initializeSpeek()
        } catch {
            BoneHUD.shared.show(error: error)
            print("朗读下一页错误:\(error.localizedDescription)")
        }
    }

    /// 跳到上一章（兼容播放/朗读）
    private func previousCatalog() {
        guard let catalogData = self.readSource?.previousCatalogData else {
            BoneHUD.shared.show(status: "已经是第一章了", showType: .info)
            self.stopSpeaking()
            return
        }
        switch self.bookType {
        case .network, .local:
            self.handleVoiceCatalog(catalog: .previousCatalog(catalogData)) { error in
                if !error.isEmpty {
                    BoneHUD.shared.show(status: error, showType: .error)
                }
            }
        case .play:
            self.handlePlayCatalog(catalog: .previousCatalog(catalogData)) { error in
                if !error.isEmpty {
                    BoneHUD.shared.show(status: error, showType: .error)
                }
            }
        case .cartoon:
            break
        }
    }
    
    /// 跳到下一章（兼容播放/朗读）
    private func nextCatalog() {
        guard let catalogData = self.readSource?.nextCatalogData else {
            BoneHUD.shared.show(status: "已播完最新章节", showType: .info)
            self.stopSpeaking()
            return
        }
        switch self.bookType {
        case .network, .local:
            self.handleVoiceCatalog(catalog: .nextCatalog(catalogData)) { error in
                if !error.isEmpty {
                    BoneHUD.shared.show(status: error, showType: .error)
                }
            }
        case .play:
            self.handlePlayCatalog(catalog: .nextCatalog(catalogData)) { error in
                if !error.isEmpty {
                    BoneHUD.shared.show(status: error, showType: .error)
                }
            }
        case .cartoon:
            break
        }
    }
    
    /// 播放上一个文件
    private func previousFile() {
        guard case .file(let file) = self.playData else {
            return
        }
        // 获取上一个文件
        guard let previousFile = try? file.previousFile(filters: [.m4a, .mp3, .mp4, .wav]) else {
            BoneHUD.shared.show(status: "已经是第一个播放文件了", showType: .error)
            return
        }
        self.playData = .file(previousFile)
        self.updateReadPlayData()
        self.initializePlay(isUpdate: false)
    }
    
    /// 播放下一个文件
    private func nextFile() {
        guard case .file(let file) = self.playData else {
            return
        }
        // 获取下一个文件
        guard let nextFile = try? file.nextFile(filters: [.m4a, .mp3, .mp4, .wav]) else {
            BoneHUD.shared.show(status: "已经是最后一个播放文件了", showType: .error)
            return
        }
        self.playData = .file(nextFile)
        self.updateReadPlayData()
        self.initializePlay(isUpdate: false)
    }
    
    /// 朗读下一段(用于朗读)
    private func nextReadParagraph() {
        // 🔍 计算从播放完毕到开始处理下一段的时间
        if let finishTime = self.playFinishTime {
            let processingStartTime = Date()
            let processingDelay = processingStartTime.timeIntervalSince(finishTime) * 1000
            print("🔍 [性能监测] 播放完毕到开始处理下一段耗时: \(String(format: "%.2f", processingDelay))ms")
        }
        
        // 判断所有段落是否已读完
        if self.currentParagraphIndex < (self.paragraphDatas.count - 1) {
            self.currentParagraphIndex += 1
            
            // 判断当前段落是否存在当前分页，否则进入下一页
            if let pageRange = self.currentPageData?.range,
               let paragraphRange = self.currentParagraphData?.range {
                let pageRangeMax = pageRange.location + pageRange.length
                if paragraphRange.location >= pageRange.location && paragraphRange.location < pageRangeMax {
                    self.nextPlayType = .currentPage
                    
                } else {
                    self.nextPlayType = .nextPage
                }
            } else {
                self.nextPlayType = .notContent
            }

        } else {
            // 下一章节不为空，并且有内容，下一章阅读，否则显示未下载
            if let nextCatalogData = self.readSource?.nextCatalogData {
                // 章节正文为空
                if nextCatalogData.contentsStatus != .hasContent {
                    self.nextPlayType = .notContent
                } else {
                    self.nextPlayType = .nextChapter
                }
            } else {
                self.nextPlayType = .notChapter
            }
        }
        
        // 🔍 记录开始播放业务处理的时间
        let playBusinessStartTime = Date()
        if let finishTime = self.playFinishTime {
            let businessDelay = playBusinessStartTime.timeIntervalSince(finishTime) * 1000
            print("🔍 [性能监测] 播放完毕到播放业务处理耗时: \(String(format: "%.2f", businessDelay))ms")
        }
        
        // 播放业务
        switch self.nextPlayType {
        case .currentPage:
            print("🔍 [性能监测] 执行当前页播放")
            self.speakSpeaking()
            
        case .nextPage:
            print("🔍 [性能监测] 执行下一页播放")
            if let nextPageData = self.readSource?.nextPageData {
                // 记录阅读章节、分页位置
                do {
                    if let oldPageData = self.currentPageData {
                        // 代理方法
                        self.multicastDelegate.invoke { delegate in
                            delegate.readPlay(self, nextPageData: nextPageData, oldPageData: oldPageData)
                        }
                    }
                    try self.setNewPlay(catalogIndex: nextPageData.catalogIndex, pageIndex: nextPageData.pageNum)
            
                } catch {
                    print("朗读下一章节错误:\(error.localizedDescription)")
                }
            }
            self.speakSpeaking()
            // 自动更新未加载章节（前一章、后一章）
            self.readSource?.autoNetworkUpdatePage()
            
        case .nextChapter, .notContent:
            print("🔍 [性能监测] 执行下一章播放")
            // 获取下一章章节，以及下一章的第一页，判断类型为nextChapter
            if let nextCatalogData = self.readSource?.nextCatalogData,
               let nextPageData = nextCatalogData.styleData?.pageDatas.first,
                self.nextPlayType == .nextChapter {
                
                // 记录阅读章节、分页位置
                do {
                    if let oldPageData = self.currentPageData {
                        // 代理方法
                        self.multicastDelegate.invoke { delegate in
                            delegate.readPlay(self, nextPageData: nextPageData, oldPageData: oldPageData)
                        }
                    }
                    try self.setNewPlay(catalogIndex: nextPageData.catalogIndex, pageIndex: nextPageData.pageNum)
                    

                } catch {
                    print("朗读下一章节错误:\(error.localizedDescription)")
                }
                self.currentParagraphIndex = 0
                self.initializeSpeek()
                
            } else {
                self.currentParagraphIndex = 0
                self.stopSpeaking()
                BoneHUD.shared.show(status: "当前章节未加载, 朗读结束", showType: .info)
            }
            
            // 自动更新未加载章节（前一章、后一章）
            self.readSource?.autoNetworkUpdatePage()
            
        case .notChapter: // 无章节，停止播放
            print("🔍 [性能监测] 无章节，停止播放")
            self.currentParagraphIndex = 0
            self.stopSpeaking()
            BoneHUD.shared.show(status: "已读到最新章节, 朗读结束", showType: .info)
        }
    }
    
    
    /// 更新语音控制
    private func audioSessionUpdate() {
        let session = AVAudioSession.sharedInstance()
        do {
            try session.setCategory(.playback, mode: .default)
            try session.setActive(true, options: .notifyOthersOnDeactivation)
            
            // 关键配置 (必须使用 .playback 类别)
//            try session.setCategory(
//                .playback,
//                mode: .default,
//                options: [.allowBluetoothA2DP, .allowAirPlay, .defaultToSpeaker]
//            )
            // 激活会话 (确保在后台维持活动)
//            try session.setActive(true, options: .notifyOthersOnDeactivation)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    /// 设置声音类型
    func setVoice(type: VoiceRoleModel) {
        do {
            try KAReadConfig.setVoice(.playRole(type))
        } catch {
            BoneHUD.shared.show(error: error)
        }
        self.initializeSpeek()
    }
    
    /// 设置语速
    func setVoice(rate: Float) {
        do {
            try KAReadConfig.setVoice(.playSpeed(rate))
            switch self.playData {
            case .read:
                switch self.roleData.playMode {
                case .speech:
                    self.speechLibrary?.setRate(value: rate)
                case .audio:
                    self.qPlayerLibrary?.setRate(value: rate)
                }
            case .play:
                self.playLibrary?.playSpeed = rate
            case .file:
                self.playLibrary?.playSpeed = rate
            case .none:
                break
            }
        } catch {
            BoneHUD.shared.show(error: error)
        }
    }
    
    /// 新页面播放，朗读播放书籍方法
    private func setNewPlay(catalogIndex: Int, pageIndex: Int) throws {
        // 章节切换时清理缓存，避免缓存过大
        self.regularCleanData()
        
        // 如果有阅读信息，初始化进度
        let promise = self.readSource?.selectCatalogOrPage(catalogIndex: catalogIndex, pageIndex: pageIndex).done { [weak self] catalogData in
            guard let self = self else { return }
            guard let catalogData = catalogData else {
                return
            }
            switch self.playData {
            case .read:
                guard let pageData = self.currentPageData else { return }
                self.playData = .read(self.getPlayBookData(), catalogData, pageData)
            case .play:
                self.playData = .play(self.getPlayBookData(), catalogData)
            default:
                break
            }
            self.updateReadPlayData()
            // 开始新的播放，重置进度
            self.playVC?.resetData()
        }.catch { error in
            BoneHUD.shared.show(error: error)
        }
        
        // 🔧 持有Promise引用
        if let promise = promise {
            self.promiseQueue.async(flags: .barrier) {
                self.activePromises.append(promise)
            }
        }
    }
    
    private func setNewPlay(file: File) {
        print("更新数据")
        self.playData = .file(file)
        self.updateReadPlayData()
        // 开始新的播放，重置进度
        self.playVC?.resetData()
    }
    
    /// 设置音调
    func setVoice(pitch: Float) {
        do {
            try KAReadConfig.setVoice(.playPitch(pitch))
        } catch {
            BoneHUD.shared.show(error: error)
        }
        self.initializeSpeek()
    }
    
    /// 设置定时器
    func setTimer(timeInterval: Int) {
        self.timer?.invalidate()
        self.timer = nil
        // 更新剩余秒数为滑动控制器的当前值
        self.remainingSeconds = timeInterval
        self.totalSecondTime = timeInterval * 60
    }
    
    /// 打开定时器
    func openTimer(_ callback: @escaping (_ minute: Int, _ second: Int) -> Void) {
        guard self.totalSecondTime > 0 else {
            callback(0, 0)
            return
        }
        // 创建新的定时器，使用新的时间间隔
        self.timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { timer in
            // 每秒更新剩余秒数
            self.totalSecondTime -= 1
            // 定时器触发后的处理逻辑
            if self.totalSecondTime <= 0 {
                self.stopSpeaking()
                timer.invalidate()
            }
            let timerMinuteAndSecond = self.timerMinuteAndSecond
            callback(timerMinuteAndSecond.minute, timerMinuteAndSecond.second)
        }
    }
    
    /// 定时器获取分秒
    var timerMinuteAndSecond: (minute: Int, second: Int) {
        get {
            let minutes = self.totalSecondTime / 60
            let seconds = self.totalSecondTime % 60
            return (minutes, seconds)
        }
    }
    
    /// 打开播放界面
    func openReadPlayVC() {
        self.playVC = ReadPlayController()
        self.playVC?.delegate = self
        self.playVC?.playData = self.playData
        self.playVC?.show()
        
        if self.isPaused {
            self.playVC?.setStatus(status: .pause)
        } else {
            self.playVC?.setStatus(status: .playing)
        }
        
        self.updateReadPlayVC()
    }
    
    /// 关闭定时
    func closeTimer() {
        self.timer?.invalidate()
        self.timer = nil
        // 更新剩余秒数为滑动控制器的当前值
        self.remainingSeconds = 0
        self.totalSecondTime = 0
    }
    
    /// 开始播放
    /// - Parameters:
    ///   - type: <#type description#>
    ///   - bookData: <#bookData description#>
    ///   - openPlayVC: <#openPlayVC description#>
    @MainActor func startPlay(data: PlayModel, openPlayVC: Bool = false) throws {
        self.clearData()
        self.playData = data
        
        switch data {
        case .read(let book, _, let page):
            var playingInfo = ReadPlayingInfo()
            playingInfo.title = book.bookName
            playingInfo.author = book.author
            playingInfo.coverImage = book.coverImageCache
            self.playingInfoData = playingInfo
            self.setBookData(bookData: book)
            
            try self.setNewPlay(catalogIndex: page.catalogIndex, pageIndex: page.pageNum)
            self.initializeSpeek()
            
        case .play(let book, let catalog):
            var playingInfo = ReadPlayingInfo()
            playingInfo.title = book.bookName
            playingInfo.author = book.author
            if let image = book.coverImageCache {
                playingInfo.coverImage = image
            } else {
                let imageView = UIImageView(frame: CGRect(x: 0, y: 0, width: 80, height: 100))
                book.updateImage(imageView) { image in
                    playingInfo.coverImage = image
                    self.playingInfoData = playingInfo
                }
            }
           
            self.playingInfoData = playingInfo
            self.setBookData(bookData: book)
            
            try self.setNewPlay(catalogIndex: catalog.catalogIndex, pageIndex: 0)
            self.initializePlay(isUpdate: true)
            
        case .file(let value):
            var playingInfo = ReadPlayingInfo()
            playingInfo.title = value.name
            playingInfo.author = value.extension ?? "" + "文件格式"
            playingInfo.coverImage = value.fileType?.getCover()
            self.playingInfoData = playingInfo
            self.setNewPlay(file: value)
            
            self.initializePlay(isUpdate: false)
        }
        print("开始朗读")
        if openPlayVC {
            self.openReadPlayVC()
        } else {
            self.updateReadPlayVC()
        }
    }
    
    deinit {
        print("ReadVoiceManage销毁，清理所有资源")
        // 🔧 清理所有Promise引用
        self.promiseQueue.sync(flags: .barrier) {
            self.activePromises.removeAll()
        }
        // 清理其他资源
        self.clearData()
        // 移除通知观察者
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - 听书播放方法
extension ReadVoiceManage {
    
    /// 初始化播放
    /// - Parameter isUpdate: 是否刷新地址
    func initializePlay(isUpdate: Bool) {
        BoneHUD.shared.loading(text: "开始播放...")
        // 播放资产
        var asset: PlayURLAsset!

        switch self.playData {
        case .play:
            // 获取当前阅读的章节、分页
            guard let currentCatalogData = self.readSource?.currentCatalogData else {
                BoneHUD.shared.show(status: "无朗读数据", showType: .error)
                return
            }
            // 文件名称
            let fileName = "\(currentCatalogData.bookId)_\(currentCatalogData.catalogName)"
            do {
                // 判断是否需要更新播放地址
                if try playRefresh(catalogData: currentCatalogData, isUpdate: isUpdate) {
                    // 更新章节播放地址
                    self.updatePlayUrl(catalogData: currentCatalogData) { [weak self] catalogData in
                        guard let self = self, !catalogData.playUrl.isEmpty else { 
                            BoneHUD.shared.show(status: "获取播放地址失败", showType: .error)
                            self?.stopSpeaking()
                            return
                        }
                        self.initializePlay(isUpdate: false)
                    }
                    return
                }
                
            } catch {
                BoneHUD.shared.show(error: error)
                self.stopSpeaking()
                return
            }

            // 开始代理方法
            self.multicastDelegate.invoke { delegate in
                delegate.readPlay(self, startPlaying: self.currentPageData)
            }
            
            // 自动更新未加载章节（前一章、后一章）
            self.readSource?.autoNetworkUpdatePage()

            // 获取url规则
            let jsConfig = UrlJSRequestModel(urlRule: currentCatalogData.playUrl)
            var playUrl = URL(string: jsConfig.url)
            if playUrl == nil {
                playUrl = URL(string: jsConfig.url.urlEncoded)
            }
            asset = .url(url: playUrl, header: jsConfig.header, fileName: fileName)

        case .file(let file):
            asset = .file(path: file.url)
        default:
            print("运行到了未知的区域")
        }
        
        // 听书是否工作
        self.isWorking = true

        // 更新语音控制
        self.audioSessionUpdate()
        
        self.playLibrary?.stop()
        self.playLibrary = nil
        
        self.playLibrary = ReadAVPlayLibrary()
        self.playLibrary?.delegate = self
        self.playLibrary?.bufferProgress({ [weak self] progress in
            guard let self = self else { return }
            // 设置加载进度
            self.playVC?.setLoading(Double(progress))
        })

        do {
            try self.playLibrary?.play(asset: asset)
            self.playLibrary?.playSpeed = self.voiceConfig.playSpeed
            BoneHUD.shared.dismiss()
            
        } catch {
            BoneHUD.shared.show(error: error)
        }
    }
    
    /// 更新播放地址请求
    private func updatePlayUrl(catalogData: ReadCatalogModel, callback: @escaping (_ catalogData: ReadCatalogModel) -> Void) {
        BoneHUD.shared.loading(text: "获取播放地址")
        self.readSource?.updateNetwork(catalogData: catalogData).done { data in
            catalogData.playUrl = data.playUrl
            callback(data)
        }.catch { error in
            BoneHUD.shared.show(error: error)
        }
    }
    
    /// 判断播放地址是否刷新
    /// - Parameters:
    ///   - catalogData: 当前章节
    ///   - isUpdate: 是否强制更新
    /// - Returns: 返回是否更新
    private func playRefresh(catalogData: ReadCatalogModel, isUpdate: Bool) throws -> Bool {
        // 播放地址为空
        let isNotPlayUrl = catalogData.playUrl.isEmpty
        // 没有播放规则
        let isNotPlayRule = catalogData.siteData?.ruleData.ruleContent.playUrl.isEmpty ?? true
        // 如果playurl为空，或者强制更新
        if isUpdate {
            return !isNotPlayRule
        }
        if isNotPlayUrl {
            guard !isNotPlayRule else {
                throw ReadPlayError.notParam("没有播放规则，无法播放")
            }
            return true
        }
        return false
    }
}

// MARK: - 书籍朗读方法
extension ReadVoiceManage {
    
    /// 初始化开始朗读
    func initializeSpeek() {
        guard !self.roleData.voiceType.isEmpty else {
            BoneHUD.shared.show(status: "检测不到系统语音包", showType: .error)
            return
        }
        BoneHUD.shared.loading(text: "开始朗读...")

        // 在后台队列执行
        DispatchQueue.global(qos: .userInitiated).async {
            // 切割句子
            do {
                self.paragraphDatas = try self.updateParagraphData()
                
            } catch {
                BoneHUD.shared.show(error: error)
                return
            }
            
            // 获取当前阅读的章节、分页
            guard let currentCatalogData = self.readSource?.currentCatalogData,
                  let currentPageData = self.currentPageData,
                  let styleData = currentCatalogData.styleData else {
                BoneHUD.shared.show(status: "无朗读数据", showType: .error)
                return
            }
            // 听书是否工作
            self.isWorking = true
            
            // 更新语音控制
            self.audioSessionUpdate()
            
            // 自动更新未加载章节（前一章、后一章）
            self.readSource?.autoNetworkUpdatePage()
            // 初始化段落索引
            self.setParagraphIndex(catalogStyle: styleData, pageData: currentPageData)
            
            // 更新 UI 操作放回主线程
            DispatchQueue.main.async {
                // 开始代理方法
                self.multicastDelegate.invoke { delegate in
                    delegate.readPlay(self, startPlaying: self.currentPageData)
                }
                
                switch self.roleData.voiceId {
                case 0:
                    self.qPlayerLibrary?.stop()
                    self.qPlayerLibrary = nil
                    
                    self.speechLibrary?.stop()
                    self.speechLibrary = ReadSpeechLibrary(playIdent: self.roleData.voiceType)
                    self.speechLibrary?.delegate = self
                    // 初始化
                    self.speechLibrary?.initSpeek()
                    
                default:
                    self.speechLibrary?.stop()
                    self.speechLibrary = nil
                    
                    self.qPlayerLibrary?.stop()
                    
                    self.qPlayerLibrary = nil
                    self.qPlayerLibrary = ReadQueuePlayerLibrary()
                    self.qPlayerLibrary?.delegate = self

                    let paragraphDatas = self.paragraphDatas
                    // 结束下载索引
                    let indexArray = self.currentParagraphIndex..<paragraphDatas.count
                    let downloadData = indexArray.map { paragraphDatas[$0] }
                    // 取消所有下载
                    self.download?.cancelAllDownloads()
                    // 初始化
                    self.download = ReadVoiceDownload()
                    self.download?.delegate = self
                    self.download?.initTask(paragraphDatas: downloadData)
                    
                    // 初始预加载当前和后续段落
                    self.preloadNextParagraphs(preloadCount: 3, isInitial: true)
                    
                    self.download?.startDownload { data in
                        do {
                            try self.qPlayerLibrary?.updateItem(ident: data.fileName.md5(), url: data.url)
                        } catch {
                            print("下载完成错误:\(error)")
                        }
                    } complete: { complete, error in
                        
                    }
                }
                // 开始朗读
                self.speakSpeaking()
            }
        }
    }
    
    /// 开始播放(仅朗读)
    private func speakSpeaking() {
        // 🔍 记录开始播放方法的时间
        let speakStartTime = Date()
        if let finishTime = self.playFinishTime {
            let speakDelay = speakStartTime.timeIntervalSince(finishTime) * 1000
            print("🔍 [性能监测] 播放完毕到speakSpeaking开始耗时: \(String(format: "%.2f", speakDelay))ms")
        }
        
        // 获取当前段落、全部段落
        guard let currentParagraphData = self.currentParagraphData,
              !currentParagraphData.text.isEmpty else {
            BoneHUD.shared.show(status: "无法获取当前段落", showType: .error)
            return
        }
        
        let playConfig = self.voiceConfig
        let readText = currentParagraphData.text
        
        switch self.roleData.playMode {
        case .speech:
            print("🔍 [性能监测] 使用系统语音播放")
            self.speechLibrary?.playSpeed = playConfig.playSpeed
            self.speechLibrary?.playPitch = playConfig.playPitch

            try? self.speechLibrary?.play(asset: .text(readText))
            
            // 🔍 记录实际开始播放的时间
            self.nextPlayStartTime = Date()
            if let finishTime = self.playFinishTime {
                let totalDelay = self.nextPlayStartTime!.timeIntervalSince(finishTime) * 1000
                print("🔍 [性能监测] 播放完毕到实际开始播放总耗时: \(String(format: "%.2f", totalDelay))ms")
            }
            
            self.currentPlaying()
            
        case .audio:
            print("🔍 [性能监测] 使用音频文件播放")
            self.qPlayerLibrary?.playSpeed = playConfig.playSpeed
            
            // 组装下载数据
            let downloadData = ReadVoiceDownload.DownloadMode(
                index: currentParagraphData.paragraphIndex,
                text: currentParagraphData.text,
                fileName: ReadVoiceDownload.getFileName(
                    .paragraph(currentParagraphData),
                    roleData: playConfig.playRole,
                    isIncomplete: currentParagraphData.isIncomplete
                )
            )

            // 🔍 记录预加载开始时间
            let preloadStartTime = Date()
            // 智能预加载下一段落的音频，减少卡顿
            self.smartPreloadNextParagraphs()
            let preloadEndTime = Date()
            let preloadTime = preloadEndTime.timeIntervalSince(preloadStartTime) * 1000
            print("🔍 [性能监测] 智能预加载耗时: \(String(format: "%.2f", preloadTime))ms")

            // 🔍 记录下载检测开始时间
            let downloadCheckStartTime = Date()
            
            // 检测当前要朗读的内容，是否已经下载
            let downloadPromise = self.download?.currentDownload(for: downloadData).done { downloadMode in
                let downloadCheckEndTime = Date()
                let downloadCheckTime = downloadCheckEndTime.timeIntervalSince(downloadCheckStartTime) * 1000
                print("🔍 [性能监测] 下载检测耗时: \(String(format: "%.2f", downloadCheckTime))ms")
                print("下载完成开始播放:【\(Date().getTime(format: "HH:mm:ss,sss"))】\(downloadMode.text)-[\(downloadMode.status)]")
                
                switch downloadMode.status {
                case .notStarted:
                    break
                case .downloading:
                    break
                case .downloaded:
                    
                    do {
                        // 🔍 记录实际开始播放的时间
                        self.nextPlayStartTime = Date()
                        if let finishTime = self.playFinishTime {
                            let totalDelay = self.nextPlayStartTime!.timeIntervalSince(finishTime) * 1000
                            print("🔍 [性能监测] 播放完毕到实际开始播放总耗时: \(String(format: "%.2f", totalDelay))ms")
                        }
                        
                        try self.qPlayerLibrary?.play(asset: .path(
                            url: downloadMode.url,
                            ident: downloadMode.fileName.md5(),
                            fileName: downloadMode.fileName)
                        )
                        // 当前播放
                        self.currentPlaying()
                    } catch {
                        BoneHUD.shared.show(error: error)
                        self.stopSpeaking()
                    }
                    // 不在每个段落播放时清理缓存，避免频繁IO操作
                    
                case .downloadError:
                    print("🔍 [性能监测] 下载失败，切换到系统语音")
                    self.speechLibrary?.stop()
                    self.speechLibrary = ReadSpeechLibrary(playIdent: self.voiceConfig.standbyRole.voiceType)
                    self.speechLibrary?.delegate = self
                    // 初始化
                    self.speechLibrary?.initSpeek()
                    self.speechLibrary?.playSpeed = playConfig.playSpeed
                    self.speechLibrary?.playPitch = playConfig.playPitch

                    // 🔍 记录实际开始播放的时间
                    self.nextPlayStartTime = Date()
                    if let finishTime = self.playFinishTime {
                        let totalDelay = self.nextPlayStartTime!.timeIntervalSince(finishTime) * 1000
                        print("🔍 [性能监测] 播放完毕到实际开始播放总耗时: \(String(format: "%.2f", totalDelay))ms")
                    }
                    
                    try? self.speechLibrary?.play(asset: .text(readText))
                    self.currentPlaying()
                }
            }.catch { error in
                BoneHUD.shared.show(error: error)
            }
            
            // 🔧 持有下载Promise引用
            if let downloadPromise = downloadPromise {
                self.promiseQueue.async(flags: .barrier) {
                    self.activePromises.append(downloadPromise)
                }
            }
           
        }
    }
    
    /// 结束播放(兼容朗读/播放)
    func stopSpeaking() {
        self.hidePlayButton()
        self.isWorking = false
        self.isPaused = true
        
        self.updateReadPlayVC()
        self.clearData()
        // 关闭定时器
        self.closeTimer()
        // 结束播放通知UI界面
        self.multicastDelegate.invoke { delegate in
            delegate.readPlay(self, stopPlaying: self.currentPageData)
        }
        // 移除远程通知
        self.removeRemoteControl()
    }
    
    /// 清理数据
    private func clearData() {
        self.speechLibrary?.stop()
        self.speechLibrary = nil
        
        // 取消所有下载
        self.download?.cancelAllDownloads()
        self.download = nil
        
        self.qPlayerLibrary?.stop()
        self.qPlayerLibrary = nil
        
        self.playVC?.isFinish()
        self.playLibrary?.stop()
        self.playLibrary = nil
        
        // 🔧 清理Promise引用
        self.promiseQueue.async(flags: .barrier) {
            self.activePromises.removeAll()
        }
        
        // 清理预加载状态
        self.preloadedParagraphIndexes.removeAll()
        
        // 🔍 清理时间监测变量
        self.playFinishTime = nil
        self.nextPlayStartTime = nil
        
        // 清理缓存
        self.regularCleanData()
    }
    
    /// 继续播放
    func continueSpeaking() {
//        print("继续播放")
        switch self.playData {
        case .read:
            self.speechLibrary?.resume()
            self.qPlayerLibrary?.resume()
        case .play:
            self.playLibrary?.resume()
        case .file:
            self.playLibrary?.resume()
        case .none:
            break
        }
        self.updateReadPlayVC()
    }
    
    /// 暂停播放
    func pauseSpeaking() {
//        print("暂停播放")
        switch self.playData {
        case .read:
            self.speechLibrary?.pause()
            self.qPlayerLibrary?.pause()
        case .play:
            self.playLibrary?.pause()
        case .file:
            self.playLibrary?.pause()
        case .none:
            break
        }
        self.updateReadPlayVC()
    }
    
}

// MARK: - 播放界面代理方法
extension ReadVoiceManage: @preconcurrency ReadPlayControllerDelegate {
 
    
    /// 拖动代理方法
    func readPlay(_ vc: ReadPlayController, playProgressbar value: Double, isSlideFinish: Bool) {
        // 拖动完成
        if isSlideFinish {
            self.isPaused = false
            self.playLibrary?.setSeek(value: value)
            self.setProgressUI()
        } else {
            let totalProgress = self.playLibrary?.durationTime ?? 0
            let currentProgress = value * totalProgress
            // 设置进度内容HH:mm
            let totalTime = self.formatTime(seconds: totalProgress)
            let currentTime = self.formatTime(seconds: currentProgress)
            
            self.playVC?.setProgressText(String(format: "%@/%@", currentTime, totalTime))
        }
    }
    
    /// 播放器按键代理
    @MainActor func readPlay(_ vc: ReadPlayController, buttonType: ReadPlayController.ButtonType, valueType: ReadPlayController.ValueType) {
        
        switch buttonType {
        case .timer:
            self.openTimer { minute, second in
                vc.setTimerButton(minute: minute, second: second)
            }
            
        case .speed:
            if case let .speed(value) = valueType {
                self.setVoice(rate: value)
            }
        case .chapter:
            // 文件播放
            if case let .file(file) = valueType {
                do {
                    try self.startPlay(data: .file(file))
                } catch {
                    BoneHUD.shared.show(error: error)
                }
            }
            // 获取章节数据
            if case let .chapter(value) = valueType {
                switch self.bookType {
                case .play:
                    do {
                        try self.setNewPlay(catalogIndex: value.catalogIndex, pageIndex: 0)
                        self.initializePlay(isUpdate: true)
                    } catch {
                        BoneHUD.shared.show(error: error)
                    }
                case .local, .network:
                    // 如果章节
                    if value.contentsStatus != .hasContent {
                        BoneHUD.shared.loading(text: "正在加载章节")
                        self.readSource?.updateNetwork(catalogData: value).done { data in
                            do {
                                BoneHUD.shared.dismiss()
                                try self.setNewPlay(catalogIndex: data.catalogIndex, pageIndex: 0)
                                self.initializeSpeek()
                            } catch {
                                BoneHUD.shared.show(error: error)
                            }
                        }.catch { error in
                            BoneHUD.shared.show(error: error)
                        }
                    }
                case .cartoon:
                    break
                }
            }
        case .originText:
            // 从阅读进入的
            let statusType = self.getStatusType(bookData: self.readBookData)
            if statusType == .readAndPlay {
                vc.close()
            } else {
                vc.hidden {
                    let readVC = BookReadViewController()
                    readVC.bookData = self.getPlayBookData()
                    
                    Default.shared().navigationController?.pushViewController(readVC, animated: true)
                }
            }
 
        case .play, .pause , .continue: // 播放/暂停
            if self.isWorking {
                if self.isPaused {
                    self.playVC?.setStatus(status: .pause)
                    self.continueSpeaking()
                } else {
                    self.playVC?.setStatus(status: .playing)
                    self.pauseSpeaking()
                }
            } else {
                self.playVC?.setStatus(status: .stop)
                do {
                    try self.startPlay(data: self.playData)
                } catch {
                    BoneHUD.shared.show(error: error)
                }
            }

        case .previous:
            switch self.playData {
            case .file:
                self.previousFile()
            default:
                self.previousCatalog()
            }
        case .next:
            switch self.playData {
            case .file:
                self.nextFile()
            default:
                self.nextCatalog()
            }
        case .close:
            self.playVC?.delegate = nil
            self.playVC = nil
            
            switch self.readStatusType {
            case .readAndPlay, .readOrPlayUnlike:
                break
            case .none, .onlyPlay:
                if self.isWorking {
                    self.showPlayButton()
                }
            }
        case .switchRole:
            if case let .role(value) = valueType {
                self.setVoice(type: value)
            }
        }
    }
}

// MARK: - 播放代理方法
extension ReadVoiceManage: ReadVoiceLibraryDelegate {
    
    /// 1.开始加载
    func playStartLoad() {
        self.playVC?.setStatus(status: .startDownload)
        BoneHUD.shared.loading(text: "网络加载中...")
    }
    
    /// 2.开始播放
    func didStartPlaying() {
        BoneHUD.shared.dismiss()
        
        self.playVC?.setStatus(status: .playing)
        // 设置进度
        self.setProgressUI()
        // 设置进度内容HH:mm
        self.playVC?.setProgressText("0:00/0:00")
        self.updateReadPlayVC()
    }
    
    /// 听书播放进度
    func playProgress(totalProgress: Float64, currentProgress: Float64) {
        guard totalProgress > 0 else {
            print("总进度为0")
            return
        }
        
        // 判断当前是否拖动中
        guard let playVC = self.playVC, !playVC.isPanGestureing else {
            return
        }
        // 设置进度
        self.setProgressUI()
        // 设置进度内容HH:mm
        let totalTime = self.formatTime(seconds: totalProgress)
        let currentTime = self.formatTime(seconds: currentProgress)
        playVC.setProgressText(String(format: "%@/%@", currentTime, totalTime))
        
        if self.isWorking {
            if self.isPaused {
                self.playVC?.setStatus(status: .pause)
            } else {
                self.playVC?.setStatus(status: .playing)
            }
        }
        
        self.updateNowPlayingInfo(totalProgress: totalProgress, currentProgress: currentProgress)
    }
    
    /// 进度条时间格式
    private func formatTime(seconds: Float64) -> String {
        guard !seconds.isNaN else { return "00:00" }
        
        let hours = Int(seconds) / 3600
        let minutes = (Int(seconds) % 3600) / 60
        let remainingSeconds = Int(seconds) % 60
        
        if hours > 0 {
            return String(format: "%02d:%02d:%02d", hours, minutes, remainingSeconds)
        } else {
            return String(format: "%02d:%02d", minutes, remainingSeconds)
        }
    }
    
    func didDisconnect(reason: String, code: WebSocketStatusCode) {
        switch code {
        case .UnsupportedData:
//            self.audioLibrary?.pause()
            self.qPlayerLibrary?.pause()
            BoneHUD.shared.show(status: "语音角色可能不正确", showType: .error)
        default:
            BoneHUD.shared.show(status: "错误代码[\(code.rawValue)]" + reason, showType: .error)
        }
    }
    
    /// 播放失败
    func didFailWithError(_ error: Error?) {
        guard let error = error else { return }
        print("播放失败:\(error)")
        BoneHUD.shared.show(error: error)
        self.playVC?.setStatus(status: .stop)
        // 暂停播放
        self.isPaused = true
        
        self.updateReadPlayVC()
        self.clearData()
        // 关闭定时器
        self.closeTimer()
    }
    
    /// 暂停播放音频
    func didPausePlaying() {
        self.playVC?.setStatus(status: .pause)
        self.multicastDelegate.invoke { delegate in
            delegate.readPlay(self, didPausePlaying: self.currentPageData, isHeadphones: false)
        }
        print(">>>暂停播放")
    }
    
    /// 恢复播放音频
    func didResumePlaying() {
        self.playVC?.setStatus(status: .playing)
        self.multicastDelegate.invoke { delegate in
            delegate.readPlay(self, didContinuePlaying: self.currentPageData)
        }
        print(">>>继续播放")
    }
    
    /// 播放完成
    func didFinishPlaying() {
        // 🔍 记录播放完毕时间
        self.playFinishTime = Date()
        print(">>>播放完成 - 时间: \(Date().getTime(format: "HH:mm:ss.SSS"))")
        
        if self.isTest {
            return
        }
        
        switch self.playData {
        case .read:
            // 当前工作中，完成后播放下一段
            if self.isWorking {
                self.nextReadParagraph()
            }
        case .play:
            if self.isWorking {
                self.playVC?.setStatus(status: .playing)
            } else {
                self.playVC?.setStatus(status: .downloading)
            }
            self.nextCatalog()
        case .file:
            if self.isWorking {
                self.playVC?.setStatus(status: .playing)
            } else {
                self.playVC?.setStatus(status: .downloading)
            }
            self.nextFile()
        case .none:
            break
        }
    }

    /// 取消播放
    func didCancelPlaying() {
        print(">>>结束播放")
        self.playVC?.setStatus(status: .stop)
        self.multicastDelegate.invoke { delegate in
            delegate.readPlay(self, stopPlaying: self.currentPageData)
        }
    }
    
    /// 本地朗读代理方法
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, willSpeakRangeOfSpeechString characterRange: NSRange, utterance: AVSpeechUtterance) {
        // 段落刚播放完成时暂停有一定概率进入下一段落播放，导致暂停失效
        // 测试模式不执行下面
        if self.isTest {
            return
        }
        if !self.isWorking {
            self.stopSpeaking()
        }
    }
}


// MARK: - 播放按钮相关方法
extension ReadVoiceManage {
    
    /// 隐藏播放按钮
    func hidePlayButton() {
        print("隐藏播放按钮")
        self.playButton.hideView()
    }
    
    /// 显示播放按钮
    func showPlayButton() {
        self.playButton.showView()
        
        if self.readStatusType == .onlyPlay || !(UIViewController.current() is BookReadViewController) {
            self.playButton.pin.bottom(Screen.tabHeight + 15).left(15)
        }
    }
    
    /// 设置播放按钮位置
    /// - Parameter point:
    func setPlayButtonPoint(_ point: CGPoint) {
        self.playButton.left = point.x
        self.playButton.top = point.y
    }
    
    /// 播放暂停/开始
    @MainActor @objc private func playButtonAction() {
        if self.isWorking {
            if self.isPaused {
                self.continueSpeaking()
            } else {
                self.pauseSpeaking()
            }
        } else {
            do {
                try self.startPlay(data: self.playData)
            } catch {
                BoneHUD.shared.show(error: error)
            }
        }
    }
        
    /// 关闭进步器，并退出朗读
    @objc private func closePlayButtonAction() {
        self.stopSpeaking()
    }
    
    /// 设置进度UI，播放器和播放按钮
    private func setProgressUI() {
        switch self.playData {
        case .read:
            let total = self.paragraphDatas.count
            var progress: Double = 0
            if total > 0 {
                progress = Double(self.currentParagraphIndex) / Double(total)
            }
            self.playButton.progress = progress
            if self.playVC != nil {
                self.playVC?.setProgress(progress)
            }
        case .play, .file:
            let playProgress = self.playLibrary?.playProgress ?? 0
            let playDuration = self.playLibrary?.playDuration ?? 0
            let progress = playDuration.isNaN ? 0 : (playProgress / playDuration)

            self.playButton.progress = progress
            if self.playVC != nil {
                self.playVC?.setProgress(progress)
            }
        case .none:
            break
        }
    }
}


// MARK: - 锁屏设置
extension ReadVoiceManage {
    
    /// 设置锁屏信息
    /// - Parameter data: 内容
    private func setPlayingInfoCenter() {
        var playingInfo = [String : Any]()
        if let image = self.playingInfoData?.coverImage {
            let newImage = MPMediaItemArtwork(boundsSize: image.size) { size in
                return image
            }
            playingInfo.updateValue(newImage, forKey: MPMediaItemPropertyArtwork)
        }
        // 歌曲名称
        if let title = self.playingInfoData?.title {
            playingInfo.updateValue(title, forKey: MPMediaItemPropertyTitle)
        }
        // 演唱者
        if let author = self.playingInfoData?.author {
            playingInfo.updateValue(author, forKey: MPMediaItemPropertyArtist)
        }
        MPNowPlayingInfoCenter.default().nowPlayingInfo = playingInfo
    }
    
    /// 更新锁屏进度
    func updateNowPlayingInfo(totalProgress: Float64, currentProgress: Float64) {
        guard var nowPlayingInfo = MPNowPlayingInfoCenter.default().nowPlayingInfo else { return }
        /// 总进度
        nowPlayingInfo.updateValue(totalProgress, forKey: MPMediaItemPropertyPlaybackDuration)
        ///
        nowPlayingInfo.updateValue(currentProgress, forKey: MPNowPlayingInfoPropertyElapsedPlaybackTime)
//        nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackRate] = rate
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo
    }
    
    /// 清除锁屏
    private func clearNowPlayingInfo() {
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nil
    }
    
    
    /// 移除远程控制
    private func removeRemoteControl() {
        // 清除锁屏信息
        self.clearNowPlayingInfo()
//        self.center.playCommand.removeTarget(nil)
//        self.center.pauseCommand.removeTarget(nil)
//        self.center.nextTrackCommand.removeTarget(nil)
//        self.center.previousTrackCommand.removeTarget(nil)
//        NotificationCenter.default.removeObserver(self, name: AVAudioSession.interruptionNotification, object: nil)
//        NotificationCenter.default.removeObserver(self, name: AVAudioSession.routeChangeNotification, object: nil)
    }
    
    @objc func handlePhoneCall(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let interruptionTypeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let interruptionType = AVAudioSession.InterruptionType(rawValue: interruptionTypeValue) else {
            return
        }
        switch interruptionType {
        case .began:
            // 音频中断开始，暂停音频播放
            print("音频中断开始，暂停音频播放")
            self.pauseSpeaking()
            
        case .ended:
            // 音频中断结束，检查是否为电话呼入并继续播放音频
            print("音频中断结束，检查是否为电话呼入并继续播放音频")
            self.continueSpeaking()
        default:
            break
        }
    }
    
    @objc func audioRouteChanged(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
              let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue) else {
            return
        }
        switch reason {
        case .newDeviceAvailable:
            // 耳机插入
            print("耳机插入 继续播放")
            self.continueSpeaking()
            
        case .oldDeviceUnavailable:
            // 耳机拔出
            print("耳机拔出 暂停播放")
            if (self.readSource?.getBookData().bookId ?? 0) > 0 {
                return
            }
            self.multicastDelegate.invoke { delegate in
                delegate.readPlay(self, didPausePlaying: self.currentPageData, isHeadphones: true)
            }
            self.pauseSpeaking()
            
        default:
            break
        }
    }
    
    
}

extension ReadVoiceManage {
    
    /// 更新当前段落
    private func updateParagraphData() throws -> [ParagraphSegmentModel] {
        guard let catalogData = self.readSource?.currentCatalogData,
              let styleData = catalogData.styleData else {
            throw ReadPlayError.notData("获取不到当前章节信息")
        }
        
        let voiceId = self.roleData.voiceId
        let voiceSource = VoiceSource()
        guard let voiceData = voiceSource.query(id: voiceId) else {
            throw ReadPlayError.databaseError("获取不到语音源信息，请尝试切换语音角色")
        }
        let paragraph = ParagraphSegmenter(
            minCount: voiceData.ruleData.minLength,
            maxCount: voiceData.ruleData.maxLength,
            catalog: styleData
        )
        
        let pageNum = self.currentPageData?.range.location ?? 0
        let paragraghList = paragraph.segmentArticle(styleData.fullContent.string, startPosition: pageNum)
        // 过滤空段落
        return paragraghList.filter { !$0.text.removeSapceAll.isEmpty }
    }
    
    /// 预加载接下来的几个段落，减少播放时的卡顿
    /// - Parameters:
    ///   - preloadCount: 预加载段落数量
    ///   - isInitial: 是否为初始预加载（会清空已预加载状态）
    private func preloadNextParagraphs(preloadCount: Int = 2, isInitial: Bool = false) {
        guard let download = self.download,
              self.currentParagraphIndex < self.paragraphDatas.count else {
            return
        }

        // 如果是初始预加载，清空已预加载状态
        if isInitial {
            self.preloadedParagraphIndexes.removeAll()
        }

        let playConfig = self.voiceConfig

        // 计算需要预加载的段落范围
        let startIndex = self.currentParagraphIndex + 1
        let endIndex = min(startIndex + preloadCount, self.paragraphDatas.count)

        // 如果没有后续段落，直接返回
        guard startIndex < endIndex else { return }

        // 检查是否有批量下载任务正在进行
        if download.isNotDownloadQueue {
            // 没有批量下载任务，可以安全地进行预加载
            // 预加载接下来的几个段落
            for index in startIndex..<endIndex {
                // 跳过已经预加载过的段落
                if self.preloadedParagraphIndexes.contains(index) {
                    continue
                }
                
                let paragraph = self.paragraphDatas[index]

                // 组装下载数据
                let downloadData = ReadVoiceDownload.DownloadMode(
                    index: paragraph.paragraphIndex,
                    text: paragraph.text,
                    fileName: ReadVoiceDownload.getFileName(
                        .paragraph(paragraph),
                        roleData: playConfig.playRole,
                        isIncomplete: paragraph.isIncomplete
                    )
                )

                // 标记为已预加载
                self.preloadedParagraphIndexes.insert(index)

                // 后台预加载，不需要等待结果
                let preloadPromise = download.currentDownload(for: downloadData).done { downloadMode in
                    print("预加载段落 \(index) 完成: \(downloadMode.fileName)")
                }.catch { error in
                    print("预加载段落 \(index) 失败: \(error)")
                    // 预加载失败时，从已预加载集合中移除
                    self.preloadedParagraphIndexes.remove(index)
                }

                // 持有Promise引用，防止被提前释放
                self.promiseQueue.async(flags: .barrier) {
                    self.activePromises.append(preloadPromise)
                }
            }
        } else {
            // 有批量下载任务正在进行，不进行预加载，避免冲突
            print("批量下载任务正在进行，跳过预加载")
        }
    }

    /// 智能预加载下一段落，只在必要时触发
    private func smartPreloadNextParagraphs() {
        // 计算当前播放进度，如果接近预加载边界才触发
        let remainingParagraphs = self.paragraphDatas.count - self.currentParagraphIndex - 1
        let preloadThreshold = 2 // 当剩余段落少于等于2个时触发预加载
        
        // 只有在接近预加载边界时才进行预加载
        if remainingParagraphs <= preloadThreshold {
            self.preloadNextParagraphs(preloadCount: 2)
        }
        
        // 清理过期的预加载状态（距离当前播放位置太远的段落）
        let cleanupThreshold = self.currentParagraphIndex - 5
        if cleanupThreshold > 0 {
            self.preloadedParagraphIndexes = self.preloadedParagraphIndexes.filter { $0 > cleanupThreshold }
        }
    }

    /// 下一段播放类型
    enum NextPlayType {
        /// 当前页阅读
        case currentPage
        /// 下一页
        case nextPage
        /// 下一章节
        case nextChapter
        /// 无章节
        case notChapter
        /// 无正文
        case notContent
    }
    
    /// 当前状态类型
    enum StatusType {
        case none
        /// 只播放
        case onlyPlay
        /// 阅读和播放同步
        case readAndPlay
        /// 阅读和播放书籍不一样
        case readOrPlayUnlike
    }

}

// MARK: - 测试方法
extension ReadVoiceManage {
    
    /// 测试(建议重新实例化一个，否则可能与正在朗读冲突)
    /// - Parameter text:
    func test(text: String, roleData: VoiceRoleModel, voiceData: VoiceModel) {
        self.audioSessionUpdate()
        self.isTest = true
        
        switch roleData.playMode {
        case .audio:
            self.isWorking = true
            self.qPlayerLibrary = ReadQueuePlayerLibrary()
            self.qPlayerLibrary?.delegate = self
            self.qPlayerLibrary?.playSpeed = self.voiceConfig.playSpeed
            
            let fileName = ReadVoiceDownload.getFileName(.text(text), roleData: roleData, isIncomplete: false)
            let downloadData = ReadVoiceDownload.DownloadMode(
                index: 0,
                text: text,
                fileName: fileName
            )
            do {
                // 清理测试产生的缓存
                let folder = try Folder.path(type: .history(.readPlay))
                let files = folder.find(fileName: fileName, matchSuffix: false)
                for file in files {
                    try file.delete()
                }
            } catch {
                print("删除测试缓存文件: \(error)")
            }
            
            self.download = ReadVoiceDownload()
            self.download?.debugVoiceData = voiceData
            self.download?.currentDownload(for: downloadData, roleData: roleData).done({ downloadMode in
                do {
                    try self.qPlayerLibrary?.play(asset: .path(url: downloadMode.url, ident: downloadMode.fileName.md5(), fileName: downloadMode.fileName))
                } catch {
                    BoneHUD.shared.show(error: error)
                }
            }).catch({ error in
                BoneHUD.shared.show(error: error)
            })
        case .speech:
            self.speechLibrary?.stop()

            self.isWorking = true
            self.speechLibrary = ReadSpeechLibrary(playIdent: roleData.voiceType)
            self.speechLibrary?.delegate = self
            // 初始化
            self.speechLibrary?.initSpeek()
            try? self.speechLibrary?.play(asset: .text(text))
        }
    }
}

// MARK: - 上一章下一章朗读/播放处理方法
extension ReadVoiceManage {
    /// 播放类型（朗读和听书的类型）
    enum PlayModel {
        /// 朗读
        case read(ReadBookModel, ReadCatalogModel, KAPageStyle)
        /// 播放
        case play(ReadBookModel, ReadCatalogModel)
        /// 播放本地文件
        case file(File)
    }
    
    /// 章节事件枚举
    enum CatalogAction {
        case previousCatalog(ReadCatalogModel)
        case nextCatalog(ReadCatalogModel)
    }
  
    
    /// 上/下一章的朗读处理方法
    private func handleVoiceCatalog(catalog: CatalogAction, callback: (_ msg: String) -> Void) {
        
        // 是否是下一章，如果否则是上一章
        var isNext = false
        var newCatalogData: ReadCatalogModel
        var errorTitle = ""
        switch catalog {
        case .previousCatalog(let value):
            isNext = false
            newCatalogData = value
            errorTitle = "朗读上一章错误:"
        case .nextCatalog(let value):
            isNext = true
            newCatalogData = value
            errorTitle = "朗读下一章错误:"
        }
        // 获取上一章或下一章的第一页
        guard let newPageData = newCatalogData.styleData?.pageDatas.first,
              let oldPageData = self.currentPageData else {
            return
        }
        self.playData = .read(self.getPlayBookData(), newCatalogData, newPageData)
        do {
            // 更新阅读记录并通知代理
            self.multicastDelegate.invoke { [weak self] delegate in
                guard let self = self else { return }
                if isNext {
                    delegate.readPlay(self, nextPageData: newPageData, oldPageData: oldPageData)
                } else {
                    delegate.readPlay(self, previousPageData: newPageData, oldPageData: oldPageData)
                }
            }
            try self.setNewPlay(catalogIndex: newPageData.catalogIndex, pageIndex: newPageData.pageNum)
            self.initializeSpeek()
        } catch {
            callback("\(errorTitle)\(error.localizedDescription)")
        }
    }

    /// 上/下一章的播放处理方法
    private func handlePlayCatalog(catalog: CatalogAction, callback: @escaping (_ msg: String) -> Void) {
        guard let currentCatalogData = self.readSource?.currentCatalogData else {
            return
        }
        guard !currentCatalogData.playUrl.isEmpty else {
            self.updatePlayUrl(catalogData: currentCatalogData) { [weak self] catalogData in
                guard let self = self else { return }
                self.handlePlayCatalog(catalog: catalog, callback: callback)
            }
            return
        }
        // 是否是下一章，如果否则是上一章
        var isNext = false
        var newCatalogData: ReadCatalogModel
        var errorTitle = ""
        switch catalog {
        case .previousCatalog(let value):
            isNext = false
            newCatalogData = value
            errorTitle = "播放上一章错误:"
        case .nextCatalog(let value):
            isNext = true
            newCatalogData = value
            errorTitle = "播放下一章错误:"
        }

        self.playData = .play(self.getPlayBookData(), newCatalogData)
        // 更新播放记录并通知代理
        self.multicastDelegate.invoke { [weak self] delegate in
            guard let self = self else { return }
            if isNext {
                delegate.readPlay(self, nextCatalogData: newCatalogData, oldCatalogData: currentCatalogData)
            } else {
                delegate.readPlay(self, previousCatalogData: newCatalogData, oldCatalogData: currentCatalogData)
            }
        }
        do {
            try self.setNewPlay(catalogIndex: newCatalogData.catalogIndex, pageIndex: 0)
            self.initializePlay(isUpdate: true)
        } catch {
            callback("\(errorTitle)\(error.localizedDescription)")
        }
    }
    
    /// 定期清理缓存
    func regularCleanData() {
        DispatchQueue.global(qos: .background).async {
            // 距离清理缓存时间差值
            let comparisonTime = Date().timeStamp - AppLocal.cleanAudioCacheTime
            // 每间隔5分钟清理一次
            if comparisonTime > (5 * 60) {
                do {
                    print("定期清理音频缓存")
                    // 1. 清理朗读音频缓存
                    let folder = try Folder.path(type: .history(.readPlay))
                    let voiceConfig = self.voiceConfig
                    try folder.regularClean(
                        maxSizeMB: voiceConfig.cacheMaxSizeMB,
                        maxAgeDays: voiceConfig.cacheMaxAgeDays
                    )
                    
                    // 2. 清理 VIMediaCache 缓存
                    // 获取 VIMediaCache 缓存目录
                    let viMediaCachePath = NSTemporaryDirectory().appending("vimedia")
                    if FileManager.default.fileExists(atPath: viMediaCachePath) {
                        do {
                            let viMediaFolder = try Folder(path: viMediaCachePath)
                            // 使用相同的缓存策略
                            try viMediaFolder.regularClean(
                                maxSizeMB: voiceConfig.cacheMaxSizeMB,
                                maxAgeDays: voiceConfig.cacheMaxAgeDays
                            )
                            print("VIMediaCache 缓存清理完成")
                        } catch {
                            print("VIMediaCache 缓存清理失败: \(error)")
                        }
                    }
                    
                    // 更新最后清理时间
                    AppLocal.cleanAudioCacheTime = Date().timeStamp
                } catch {
                    print("定期清理失败: \(BoneHUD.shared.handleError(error: error))")
                }
            }
        }
    }
}

extension String {
    
    /// 计算字符串末尾的空格数
    var trailingSpacesCount: Int {
        let whitespaceCharacterSet = CharacterSet.whitespaces
        if let range = rangeOfCharacter(from: whitespaceCharacterSet.inverted, options: .backwards) {
            return distance(from: range.upperBound, to: endIndex)
        } else {
            return count
        }
    }
    
    /// 计算字符串后面的换行/空格数量（用于显示当前标记的文字长度）
    var trailingSpacesAndNewlinesCount: Int {
        // 创建一个包含空格和换行符的字符集
        let whitespaceAndNewlineCharacterSet = CharacterSet.whitespacesAndNewlines

        // 找到最后一个不是空格或换行符的字符
        if let range = rangeOfCharacter(from: whitespaceAndNewlineCharacterSet.inverted, options: .backwards) {
            return distance(from: range.upperBound, to: endIndex)
        } else {
            // 如果字符串中只包含空格和换行符，返回字符串的总长度
            return count
        }
    }
    
}

