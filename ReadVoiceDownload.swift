//  ParsingBook
//
//  Created by Xu<PERSON><PERSON> on 2024/11/14.
//

import UIKit
import PromiseKit

// MARK: - 下载管理类
class ReadVoiceDownload: NSObject {

    var debugVoiceData: VoiceModel?

    weak var delegate: ReadVoiceLibraryDelegate?

    /// 标记是否取消下载
    private var isCancelled = false

    /// 当前播放的源
    var currentVoiceSource = VoiceRuleSource()
    /// 批量下载用
    private var voiceSource = VoiceRuleSource()
    /// 无下载任务
    var isNotDownloadQueue: Bool {
        return self.downloadQueue.isEmpty
    }
    /// 总下载任务
    private var downloadQueue = [DownloadMode]()
    /// 进度回调
    private var progressCallback: ((_ data: DownloadMode) -> Void)?
    /// 完成回调
    private var completeCallback: ((_ success: Bool, _ error: String) -> Void)?
    /// 语音角色
    private var playRole = VoiceRoleModel()

    private var voiceRuleData: VoiceModel!

    // 添加并发管理器
    private lazy var concurrentManager = PromiseConcurrentManager(
        config: ConcurrentConfig(
            maxConcurrency: 3,  // 最大并发数
            retryCount: 3,      // 失败重试3次
            retryDelay: 2.0,    // 重试间隔2秒
            batchDelay: 0.5     // 批次间隔0.5秒
        )
    )
    
    // 🔧 添加Promise持有数组，防止Promise被提前释放
    private var activePromises: [Promise<DownloadMode>] = []
    private let promiseQueue = DispatchQueue(label: "com.readvoice.promise", attributes: .concurrent)

    func getVoiceRule(roleData: VoiceRoleModel) -> VoiceModel {
        // id相同，直接返回
        if let voiceData = self.voiceRuleData, voiceData.id == roleData.voiceId {
            return self.voiceRuleData
        }
        let voiceSource = VoiceSource()
        guard let voiceRuleData = voiceSource.query(id: roleData.voiceId) else {
            return VoiceModel()
        }
        return voiceRuleData
    }

    override init() {
        super.init()
    }
    
    // 🔧 添加deinit方法，确保对象销毁时清理Promise引用
    deinit {
        print("ReadVoiceDownload销毁，清理Promise引用")
        self.cancelAllDownloads()
    }

    /// 初始化任务
    func initTask(paragraphDatas: [ParagraphSegmentModel]) {
        let readConfig = KAReadConfig.shared.getData()
        self.playRole = readConfig.voiceConfig.playRole

        self.downloadQueue = paragraphDatas.enumerated().map { index, paragraph in
            DownloadMode(
                index: index,
                text: paragraph.text,
                fileName: ReadVoiceDownload.getFileName(
                    .paragraph(paragraph),
                    roleData: self.playRole,
                    isIncomplete: paragraph.isIncomplete
                )
            )
        }
        print("初始化任务:\(self.downloadQueue.count)")
    }


    /// 获取不存在的文件
    private func checkIfFileExists(downloads: [DownloadMode]) -> Promise<[DownloadMode]> {
        return Promise { seal in
            var newDownloads = [DownloadMode]()
            for download in downloads {
                if KAVoiceFilesConfig.getFile(path: .history(.readPlay), fileName: download.fileName) == nil {
                    newDownloads.append(download)
                }
            }
            print("newDownloads:\(newDownloads)")
            if newDownloads.isEmpty {
                seal.reject(NSError(domain: "没有需要下载的文件", code: -1))
            } else {
                seal.fulfill(newDownloads)
            }
        }
    }

    /// 开始下载任务 - 使用PromiseConcurrentManager改进
    func startDownload(progress: @escaping (_ data: DownloadMode) -> Void,
                       complete: @escaping (_ success: Bool, _ error: String) -> Void) {
        self.progressCallback = progress
        self.completeCallback = complete
        self.isCancelled = false

        // 🔧 清理之前的Promise引用
        self.promiseQueue.async(flags: .barrier) {
            self.activePromises.removeAll()
        }

        // 重置并发管理器状态
        self.concurrentManager.reset()

        let mainPromise = self.checkIfFileExists(downloads: self.downloadQueue).then { downloadData -> Promise<[Swift.Result<DownloadMode, Error>]> in
            self.downloadQueue = downloadData

            // 创建任务数组
            let tasks = downloadData.map { mode in
                return { () -> Promise<DownloadMode> in
                    // 检查是否已取消
                    if self.isCancelled {
                        return Promise(error: ConcurrentError.cancelled)
                    }
                    let promise = self.fetchDownloadMode(for: mode)
                    
                    // 🔧 将Promise添加到持有数组中
                    self.promiseQueue.async(flags: .barrier) {
                        self.activePromises.append(promise)
                    }
                    return promise
                }
            }

            // 使用连续并发执行模式，保持固定并发数
            return self.concurrentManager.executeWithContinuousConcurrency(tasks) { completedCount, totalCount, result in
                // 进度回调
                if let result = result {
                    switch result {
                    case .success(let downloadMode):
                        DispatchQueue.main.async {
                            self.progressCallback?(downloadMode)
                        }
                    case .failure(let error):
                        print("下载失败: \(error.localizedDescription)")
                    }
                }
            }
        }
        
        // 🔧 持有主Promise，确保不被释放
        mainPromise.done { results in
            // 处理所有结果
            let successCount = results.filter {
                if case .success(let mode) = $0, mode.status == .downloaded {
                    return true
                }
                return false
            }.count

            let message = "下载完成: \(successCount)/\(results.count)"
            complete(successCount > 0, message)
            
            // 🔧 清理Promise引用
            self.promiseQueue.async(flags: .barrier) {
                self.activePromises.removeAll()
            }
        }.catch { error in
            if let concurrentError = error as? ConcurrentError, concurrentError == .cancelled {
                complete(false, "下载已取消")
            } else {
                complete(false, "下载失败: \(error.localizedDescription)")
            }
            
            // 🔧 清理Promise引用
            self.promiseQueue.async(flags: .barrier) {
                self.activePromises.removeAll()
            }
        }
        
        // 🔧 将主Promise也添加到持有数组中
        self.promiseQueue.async(flags: .barrier) {
            self.activePromises.append(mainPromise.asVoid().map { DownloadMode(index: -1, text: "", fileName: "") })
        }
        
        // 处理文件不存在的情况
        mainPromise.catch { error in
            self.downloadQueue.removeAll()
            print("开始下载任务error:\(error)")
            complete(false, "没有需要下载的文件")
            
            // 🔧 清理Promise引用
            self.promiseQueue.async(flags: .barrier) {
                self.activePromises.removeAll()
            }
        }
    }

    func currentDownload(for mode: DownloadMode, roleData: VoiceRoleModel? = nil) -> Promise<DownloadMode> {
        var updatedMode = mode
        
        // 首先检查文件是否已经存在
        if let fileURL = KAVoiceFilesConfig.getFile(path: .history(.readPlay), fileName: mode.fileName)?.url {
            print("文件已存在，直接返回: \(mode.fileName)")
            updatedMode.url = fileURL
            updatedMode.status = .downloaded
            return Promise.value(updatedMode)
        }
        
        // 文件不存在，继续下载流程
        print("文件不存在，开始处理: \(mode.fileName)")
        updatedMode.status = .downloading
        let roleData = roleData ?? self.playRole

        let voiceData = self.debugVoiceData ?? self.getVoiceRule(roleData: roleData)
        let sensitive = voiceData.extendData.sensitive + "\n" + voiceData.ruleData.sensitive
        // 处理文本
        let text = self.processText(mode.text, sensitive: sensitive)

        // 根据不同的语音ID请求不同的语音合成服务
        let promise: Promise<URL>

        switch roleData.voiceId {
        case 1: // 大声朗读
            let msTTSSource = MSTTSSource()
            promise = msTTSSource.request(text: text, roleData: roleData, fileName: mode.fileName)

        default:
            // 语音请求合成
            self.currentVoiceSource.debugVoiceData = self.debugVoiceData
            promise = self.currentVoiceSource.request(text: text, roleData: roleData, fileName: mode.fileName)
        }
        
        // 🔧 持有Promise引用，避免被提前释放
        let finalPromise = promise.map { fileURL in
            print("语音合成完成: \(mode.fileName)")
            updatedMode.url = fileURL
            updatedMode.status = .downloaded
            return updatedMode
        }.recover { error -> Promise<DownloadMode> in
            print("单独请求下载error:\(error)")
            updatedMode.status = .downloadError
            return Promise.value(updatedMode)
        }
        
        // 🔧 将Promise添加到持有数组中
        self.promiseQueue.async(flags: .barrier) {
            self.activePromises.append(finalPromise)
        }
        
        return finalPromise
    }

    /// 单独请求下载 - 简化版本，移除了批次管理逻辑
    private func fetchDownloadMode(for mode: DownloadMode, roleData: VoiceRoleModel? = nil) -> Promise<DownloadMode> {
        var updatedMode = mode
        updatedMode.status = .downloading
        let roleData = roleData ?? self.playRole

        let voiceData = self.debugVoiceData ?? self.getVoiceRule(roleData: roleData)
        let sensitive = voiceData.extendData.sensitive + "\n" + voiceData.ruleData.sensitive
        // 处理文本
        let text = self.processText(mode.text, sensitive: sensitive)

        // 根据不同的语音ID请求不同的语音合成服务
        let promise: Promise<URL>

        switch roleData.voiceId {
        case 1: // 大声朗读
            let msTTSSource = MSTTSSource()
            promise = msTTSSource.request(text: text, roleData: roleData, fileName: mode.fileName)

        default:
            // 语音请求合成
            self.voiceSource.debugVoiceData = self.debugVoiceData
            promise = self.voiceSource.request(text: text, roleData: roleData, fileName: mode.fileName)
        }

        // 🔧 使用Promise链，确保正确的错误处理和状态管理
        let finalPromise = promise.map { fileURL in
            updatedMode.url = fileURL
            updatedMode.status = .downloaded
            return updatedMode
        }.recover { error -> Promise<DownloadMode> in
            print("单独请求下载error:\(error)")
            updatedMode.status = .downloadError
            return Promise.value(updatedMode)
        }
        
        return finalPromise
    }

    /// 取消所有下载 - 使用并发管理器的取消机制
    func cancelAllDownloads() {
        self.isCancelled = true
        // 使用并发管理器的取消机制
        self.concurrentManager.cancel()
        self.downloadQueue.removeAll() // 清空队列
        self.completeCallback?(false, "下载已取消")
        print("所有下载任务已取消")
    }
}

extension ReadVoiceDownload {

    /// 下载状态
    enum DownloadStatus {
        case notStarted // 未下载
        case downloading // 下载中
        case downloaded // 已下载
        case downloadError // 下载失败
    }

    /// 下载文件
    struct DownloadMode {
        var index: Int
        var text: String
        var fileName: String
        var url: URL?
        var status: DownloadStatus = .notStarted // 初始状态为未下载
    }

    /// 获取文件名
    static func getFileName(_ fileNameType: FileNameType, roleData: VoiceRoleModel, isIncomplete: Bool) -> String {
        let suffix = "\(roleData.voiceId)_\(roleData.voiceType)_\(roleData.voiceStyle)"
        let fileName: String
        switch fileNameType {
        case .paragraph(let paragraph):
            // 使用队列索引确保文件名唯一性，同时保持缓存能力
            let uniqueContent = "\(paragraph.bookId)_\(paragraph.catalogIndex)_\(paragraph.paragraphIndex)_\(isIncomplete)"
            fileName = uniqueContent + "\(paragraph.text)_\(suffix)".md5()
            print("生成文件名: \(fileName) 对应文本: \(paragraph.text.prefix(50))... 是否完整: \(isIncomplete)")
        case .text(let text):
            fileName = text
        }
        return fileName
    }

    /// 处理文本
    private func processText(_ text: String, sensitive: String) -> String {
        var newText = text.removeSEHeadAndTail
            .replacingOccurrences(of: "\r\n", with: "。")
            .replacingOccurrences(of: "\r", with: "。")
            .replacingOccurrences(of: "\n", with: "。")

        // 敏感词处理
        let array = sensitive.components(separatedBy: "\n")
        for data in array {
            let values = data.components(separatedBy: "=")
            if values.count == 2 {
                newText = newText.replacingOccurrences(of: values[0], with: values[1])
            }
        }
        return newText
    }

    /// 文件名称
    enum FileNameType {
        case paragraph(ParagraphSegmentModel)
        case text(String)
    }
}
