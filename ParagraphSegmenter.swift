//
//  ParagraphSegmenter.swift
//  ParsingBook
//
//  Created by <PERSON><PERSON><PERSON> on 2024/12/2.
//

import UIKit


// MARK: - 段落分词器
class ParagraphSegmenter {
    /// 最小字符数限制
    let minCharacterCount: Int
    /// 最大字符数限制
    let maxCharacterCount: Int
    /// 句子切割符
    let sentenceDelimiterSet: CharacterSet
    /// 分句切割符
    let subSentenceDelimiterSet: CharacterSet
    /// 分词切割符
    let wordDelimiterSet: CharacterSet
    /// 特殊符号过滤器
    let specialCharacterSet: CharacterSet
    /// 字符集符号
    let symbolCharacterSet: CharacterSet
    /// 章节数据
    let catalog: CatalogStyle

    init(minCount: Int, maxCount: Int, catalog: CatalogStyle) {
        self.minCharacterCount = minCount
        self.maxCharacterCount = maxCount
        self.catalog = catalog
        self.sentenceDelimiterSet = CharacterSet(charactersIn: ".。?？!！…")
        self.subSentenceDelimiterSet = CharacterSet(charactersIn: ",，、;；")
        self.wordDelimiterSet = CharacterSet.whitespacesAndNewlines.union(.punctuationCharacters)
        self.specialCharacterSet = CharacterSet(charactersIn: #"%¥$#@~`^&*《》<>{}【】[]|\-()（）「」"#)
        self.symbolCharacterSet = CharacterSet.symbols
            .union(.punctuationCharacters)
            .union(.whitespacesAndNewlines)
    }
    
    /// 切割分句
    func segmentArticle(_ text: String, startPosition: Int = 0) -> [ParagraphSegmentModel] {
        let words = self.generateWords(text: text)
        let paragraphs = self.splitParagraphs(words)

        var results: [ParagraphSegmentModel] = []
        var paragraphIndex = 0

        var i = 0
        while i < paragraphs.count {
            let paragraph = paragraphs[i]

            if self.isOnlySymbols(paragraph) {
                i += 1
                continue
            }
            let paragraphLength = self.sumLengths(of: paragraph)

            if paragraphLength < self.minCharacterCount {
                var combinedParagraph = paragraph
                var combinedLength = paragraphLength

                var j = i + 1
                while combinedLength < self.minCharacterCount && j < paragraphs.count {
                    let nextParagraph = paragraphs[j]
                    if self.isOnlySymbols(nextParagraph) {
                        j += 1
                        continue
                    }
                    let newlineWord = ParagraphSegmentWord(text: "\n", range: NSRange(location: 0, length: 1))
                    combinedParagraph.append(newlineWord)
                    combinedParagraph += nextParagraph
                    combinedLength += 1 + sumLengths(of: nextParagraph)
                    j += 1
                }

                let segment = createSegment(from: combinedParagraph, type: .paragraph, index: paragraphIndex)
                results.append(segment)
                paragraphIndex += 1
                i = j
            } else if paragraphLength > maxCharacterCount {
                let sentences = splitSentences(paragraph)
                let processedSegments = processSegments(
                    segments: sentences,
                    type: .sentence,
                    paragraphIndex: &paragraphIndex
                )
                results.append(contentsOf: processedSegments)
                i += 1
            } else {
                let segment = createSegment(from: paragraph, type: .paragraph, index: paragraphIndex)
                results.append(segment)
                paragraphIndex += 1
                i += 1
            }
        }
        if startPosition == 0 {
            return results
        }
        return self.adjustSegments(results, startPosition: startPosition)
    }
    
    /// 从开始位置切割分句
    private func adjustSegments(_ segments: [ParagraphSegmentModel], startPosition: Int) -> [ParagraphSegmentModel] {
        var adjustedSegments: [ParagraphSegmentModel] = []
        
        for segment in segments {
            if startPosition >= segment.range.location + segment.range.length {
                // startPosition在此段落之后，跳过该段落
                continue
            } else if startPosition > segment.range.location && startPosition < segment.range.location + segment.range.length {
                // startPosition在此段落中间，需要切割该段落
                let splitIndex = startPosition - segment.range.location
                let splitLength = segment.range.length - splitIndex
                let secondPartText = String(segment.text.suffix(from: segment.text.index(segment.text.startIndex, offsetBy: splitIndex)))
                
                // 创建新的段落模型，仅保留从splitIndex开始的部分
                let newSegment = ParagraphSegmentModel(
                    text: secondPartText,
                    range: NSRange(location: startPosition, length: splitLength),
                    type: segment.type,
                    bookId: segment.bookId,
                    catalogIndex: segment.catalogIndex,
                    paragraphIndex: segment.paragraphIndex,
                    words: self.splitWords(segment.words, from: splitIndex),
                    isIncomplete: true // 拆分了
                )
                adjustedSegments.append(newSegment)
            } else if startPosition <= segment.range.location {
                // startPosition在此段落之前或正好在段落起始处，保留整个段落
                adjustedSegments.append(segment)
            }
        }
        return adjustedSegments
    }
    
    /// 辅助函数：根据字符索引切割单词数组
    private func splitWords(_ words: [ParagraphSegmentWord]?, from splitIndex: Int) -> [ParagraphSegmentWord]? {
        guard let words = words else { return nil }
        var accumulatedLength = 0
        var splitWordIndex = 0
        
        while splitWordIndex < words.count && accumulatedLength + words[splitWordIndex].text.count <= splitIndex {
            accumulatedLength += words[splitWordIndex].text.count
            splitWordIndex += 1
        }
        
        // 切割当前单词
        if splitWordIndex < words.count && accumulatedLength < splitIndex {
            let word = words[splitWordIndex]
            let wordSplitIndex = splitIndex - accumulatedLength
            let secondPart = String(word.text.suffix(from: word.text.index(word.text.startIndex, offsetBy: wordSplitIndex)))
            
            // 仅保留从splitIndex开始的部分
            let newWord = ParagraphSegmentWord(
                text: secondPart,
                range: NSRange(location: word.range.location + wordSplitIndex, length: word.text.count - wordSplitIndex))
            var newWords = Array(words.dropFirst(splitWordIndex))
            newWords[0] = newWord
            return newWords
        }
        
        return Array(words.dropFirst(splitWordIndex))
    }


    private func generateWords(text: String) -> [ParagraphSegmentWord] {
        let nsText = text as NSString
        var words: [ParagraphSegmentWord] = []

        nsText.enumerateSubstrings(in: NSRange(location: 0, length: nsText.length), options: .byComposedCharacterSequences) { (substring, substringRange, _, _) in
            if let substring = substring {
                let substring = self.replaceSpecialCharactersWithSpace(in: substring)
                let word = ParagraphSegmentWord(text: substring, range: substringRange)
                words.append(word)
            }
        }

        return words
    }
    
    // 替换特殊符号为一个空格
    private func replaceSpecialCharactersWithSpace(in text: String) -> String {
        if let scalar = text.unicodeScalars.first,
           specialCharacterSet.contains(scalar) {
            return " "
        }
        return text
    }
    


    /// 段落切割
    private func splitParagraphs(_ words: [ParagraphSegmentWord]) -> [[ParagraphSegmentWord]] {
        return splitWords(words, delimiter: "\n")
    }

    /// 句子切割
    private func splitSentences(_ words: [ParagraphSegmentWord]) -> [[ParagraphSegmentWord]] {
        return splitWordsByCharacterSet(words, delimiterSet: sentenceDelimiterSet)
    }

    /// 分句切割
    private func splitSubSentences(_ words: [ParagraphSegmentWord]) -> [[ParagraphSegmentWord]] {
        return splitWordsByCharacterSet(words, delimiterSet: subSentenceDelimiterSet)
    }

    /// 分词切割
    private func splitIntoWords(_ words: [ParagraphSegmentWord]) -> [[ParagraphSegmentWord]] {
        return splitWordsByCharacterSet(words, delimiterSet: wordDelimiterSet)
    }

    private func splitWords(_ words: [ParagraphSegmentWord], delimiter: String) -> [[ParagraphSegmentWord]] {
        var segments: [[ParagraphSegmentWord]] = []
        var buffer: [ParagraphSegmentWord] = []

        for word in words {
            buffer.append(word)
            if word.text == delimiter {
                segments.append(buffer)
                buffer.removeAll()
            }
        }

        if !buffer.isEmpty {
            segments.append(buffer)
        }

        return segments
    }

    private func splitWordsByCharacterSet(_ words: [ParagraphSegmentWord], delimiterSet: CharacterSet) -> [[ParagraphSegmentWord]] {
        var segments: [[ParagraphSegmentWord]] = []
        var buffer: [ParagraphSegmentWord] = []

        for word in words {
            buffer.append(word)
            if let scalar = word.text.unicodeScalars.first, delimiterSet.contains(scalar) {
                segments.append(buffer)
                buffer.removeAll()
            }
        }

        if !buffer.isEmpty {
            segments.append(buffer)
        }

        return segments
    }

    private func processSegments(segments: [[ParagraphSegmentWord]], type: ParagraphSegmentType, paragraphIndex: inout Int) -> [ParagraphSegmentModel] {
        var output: [ParagraphSegmentModel] = []
        var buffer: [ParagraphSegmentWord] = []
        var bufferLength = 0

        var i = 0
        while i < segments.count {
            let segmentWords = segments[i]

            if isOnlySymbols(segmentWords) {
                i += 1
                continue
            }

            let segmentLength = sumLengths(of: segmentWords)

            if segmentLength >= minCharacterCount && segmentLength <= maxCharacterCount {
                let segment = createSegment(from: segmentWords, type: type, index: paragraphIndex)
                output.append(segment)
                paragraphIndex += 1
                i += 1
            } else if segmentLength < minCharacterCount {
                buffer += segmentWords
                bufferLength += segmentLength

                var j = i + 1
                while bufferLength < minCharacterCount && j < segments.count {
                    let nextSegmentWords = segments[j]
                    if isOnlySymbols(nextSegmentWords) {
                        j += 1
                        continue
                    }
                    buffer += nextSegmentWords
                    bufferLength += sumLengths(of: nextSegmentWords)
                    j += 1
                }

                if bufferLength >= minCharacterCount {
                    let segment = createSegment(from: buffer, type: type, index: paragraphIndex)
                    output.append(segment)
                    paragraphIndex += 1
                    buffer.removeAll()
                    bufferLength = 0
                    i = j
                } else {
                    if !output.isEmpty {
                        let lastSegment = output.removeLast()
                        let combinedWords = lastSegment.words! + buffer
                        let combinedSegment = createSegment(from: combinedWords, type: lastSegment.type, index: lastSegment.paragraphIndex)
                        output.append(combinedSegment)
                        buffer.removeAll()
                        bufferLength = 0
                    } else {
                        let segment = createSegment(from: buffer, type: type, index: paragraphIndex)
                        output.append(segment)
                        paragraphIndex += 1
                        buffer.removeAll()
                        bufferLength = 0
                    }
                    i = j
                }
            } else {
                if type == .sentence {
                    let subSentences = splitSubSentences(segmentWords)
                    let processedSegments = processSegments(segments: subSentences, type: .subSentence, paragraphIndex: &paragraphIndex)
                    output.append(contentsOf: processedSegments)
                } else if type == .subSentence {
                    let words = splitIntoWords(segmentWords)
                    let processedSegments = processSegments(segments: words, type: .word, paragraphIndex: &paragraphIndex)
                    output.append(contentsOf: processedSegments)
                } else {
                    let segment = createSegment(from: segmentWords, type: type, index: paragraphIndex)
                    output.append(segment)
                    paragraphIndex += 1
                }
                i += 1
            }
        }

        return output
    }

    private func createSegment(from words: [ParagraphSegmentWord], type: ParagraphSegmentType, index: Int) -> ParagraphSegmentModel {
        let text = words.map { $0.text }.joined()
        let startLocation = words.first?.range.location ?? 0
        let endWord = words.last
        let endLocation = (endWord?.range.location ?? 0) + (endWord?.range.length ?? 0)
        let range = NSRange(location: startLocation, length: endLocation - startLocation)
        return ParagraphSegmentModel(
            text: text,
            range: range,
            type: type,
            bookId: catalog.bookId,
            catalogIndex: catalog.catalogIndex,
            paragraphIndex: index,
            words: words
        )
    }

    private func sumLengths(of words: [ParagraphSegmentWord]) -> Int {
        return words.reduce(0) { $0 + $1.length }
    }

    /// 非字符符号判断
    private func isOnlySymbols(_ words: [ParagraphSegmentWord]) -> Bool {
        for word in words {
            if let scalar = word.text.unicodeScalars.first,
                !symbolCharacterSet.contains(scalar) {
                return false
            }
        }
        return true
    }

}

// MARK: - 分割类型
enum ParagraphSegmentType {
    case paragraph
    case sentence
    case subSentence
    case word
}

// MARK: - Word 类
class ParagraphSegmentWord {
    var text: String
    var range: NSRange
    var length: Int

    init(text: String, range: NSRange) {
        self.text = text
        self.range = range
        self.length = text.count
    }
}

// MARK: - 分割结果模型
struct ParagraphSegmentModel {
    var text: String
    var range: NSRange
    let type: ParagraphSegmentType

    let bookId: Int
    let catalogIndex: Int
    let paragraphIndex: Int
    /// 用于存储组成该段的 Word 数组
    var words: [ParagraphSegmentWord]?
    /// 不完整的句子（多用于从中间分页开始朗读，第一句被切割）
    var isIncomplete = false
}
